2025-08-12 21:00:41,600 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:00:41,600 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:00:41,600 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:00:41,600 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:00:41,874 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:00:41,875 - [34msrc.config_validator[0m - [32mIN<PERSON>O[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-12 21:00:41,877 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:00:41,877 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:00:41,889 - [34msrc.performance[0m - [32mINFO[0m - start_download:106 - 开始监控下载: https://music.163.com/song?id=123456 (索引: 0)
2025-08-12 21:00:41,889 - [34msrc.performance[0m - [32mINFO[0m - finish_download:126 - 下载完成: https://music.163.com/song?id=123456 - 1.0MB, 0.0s, 2114.2MB/s
2025-08-12 21:00:41,889 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-12 21:00:41,889 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:100 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:00:41,889 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:133 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:146 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_72490_1755003641
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:153 - 清理函数已注册
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:166 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_72490_1755003641
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:00:41,890 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:00:41,952 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:00:41,953 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:00:48,374 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:00:48,378 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:00:48,378 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:00:48,378 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:00:48,419 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-12 21:01:11,661 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:01:11,661 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:01:11,661 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:01:11,661 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:01:11,690 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-12 21:01:11,691 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:01:11,691 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:01:11,691 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:01:11,691 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:153 - ✓ PyInstaller环境检测完成
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:154 - 🚀 程序启动 - 网易云音乐下载器 Python版本
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:155 - 📋 开始执行主程序流程，当前阶段: 预初始化
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:164 - 📖 开始加载配置文件...
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:166 - ✓ 配置文件加载完成，开始验证配置有效性
2025-08-12 21:01:11,691 - [34m__main__[0m - [32mINFO[0m - main:171 - 🔍 开始配置验证和依赖项检查...
2025-08-12 21:01:11,691 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:01:11,692 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-12 21:01:11,693 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:01:11,693 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:01:11,693 - [34m__main__[0m - [32mINFO[0m - main:186 - 📦 检查Python依赖包完整性...
2025-08-12 21:01:11,693 - [34m__main__[0m - [32mINFO[0m - main:191 - ✓ 所有Python依赖包检查通过
2025-08-12 21:01:11,693 - [34m__main__[0m - [32mINFO[0m - main:200 - 🏗️ 开始初始化工作环境...
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:100 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:133 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:146 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_72655_1755003671
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:153 - 清理函数已注册
2025-08-12 21:01:11,693 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-12 21:01:11,694 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-12 21:01:11,694 - [34m__main__[0m - [32mINFO[0m - main:209 - ✓ 工作环境初始化成功，所有目录已准备就绪
2025-08-12 21:01:11,694 - [34m__main__[0m - [32mINFO[0m - main:210 - 📁 下载目录、临时目录、浏览器数据目录已创建并配置完成
2025-08-12 21:01:11,694 - [34m__main__[0m - [32mINFO[0m - main:220 - 🎨 显示程序欢迎界面和配置信息...
2025-08-12 21:01:11,694 - [34m__main__[0m - [32mINFO[0m - main:226 - ✓ 程序信息展示完成，用户界面已准备就绪
2025-08-12 21:02:42,967 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:02:42,969 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:166 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_72655_1755003671
2025-08-12 21:02:42,969 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:02:42,969 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:02:42,969 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:02:42,969 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:02:42,970 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:02:42,970 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:02:42,970 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:02:42,970 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:02:42,970 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:08:56,564 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:08:56,564 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:08:56,564 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:08:56,564 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:08:56,738 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:08:56,739 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:211 - FFmpeg 依赖验证通过
2025-08-12 21:08:56,740 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:245 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:08:56,740 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:08:56,745 - [34msrc.performance[0m - [32mINFO[0m - start_download:106 - 开始监控下载: https://music.163.com/song?id=123456 (索引: 0)
2025-08-12 21:08:56,745 - [34msrc.performance[0m - [32mINFO[0m - finish_download:126 - 下载完成: https://music.163.com/song?id=123456 - 1.0MB, 0.0s, 9615.4MB/s
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:100 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:133 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:146 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_73333_1755004136
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:153 - 清理函数已注册
2025-08-12 21:08:56,745 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:166 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_73333_1755004136
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:160 - 开始环境清理
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:175 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:190 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:216 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:08:56,746 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 环境清理完成
2025-08-12 21:12:50,011 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:12:50,011 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:12:50,011 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:12:50,011 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:12:50,194 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:12:50,196 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:211 - FFmpeg 依赖验证通过
2025-08-12 21:12:50,197 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:245 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:12:50,197 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:12:50,202 - [34msrc.performance[0m - [32mINFO[0m - start_download:138 - 开始监控下载: https://music.163.com/song?id=123456 (索引: 0)
2025-08-12 21:12:50,202 - [34msrc.performance[0m - [32mINFO[0m - finish_download:176 - 下载完成: https://music.163.com/song?id=123456 - 1.0MB, 0.0s, 8064.5MB/s
2025-08-12 21:12:50,202 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:100 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:133 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:159 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74335_1755004370
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:160 - ✓ 数据隔离: 使用独立的浏览器数据目录
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:167 - 清理函数已注册
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-12 21:12:50,203 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 开始环境清理
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:189 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74335_1755004370
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:203 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:224 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [31mERROR[0m - cleanup_environment:211 - 环境清理失败: name 'time' is not defined
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:180 - 开始环境清理
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:203 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:224 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:12:50,204 - [34msrc.env_setup[0m - [31mERROR[0m - cleanup_environment:211 - 环境清理失败: name 'time' is not defined
2025-08-12 21:14:52,603 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:14:52,603 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:14:52,604 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:14:52,604 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:14:52,775 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:14:52,777 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:211 - FFmpeg 依赖验证通过
2025-08-12 21:14:52,778 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:245 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:14:52,778 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:14:52,782 - [34msrc.performance[0m - [32mINFO[0m - start_download:138 - 开始监控下载: https://music.163.com/song?id=123456 (索引: 0)
2025-08-12 21:14:52,782 - [34msrc.performance[0m - [32mINFO[0m - finish_download:176 - 下载完成: https://music.163.com/song?id=123456 - 1.0MB, 0.0s, 9523.8MB/s
2025-08-12 21:14:52,782 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:51 - 开始环境初始化
2025-08-12 21:14:52,782 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:14:52,782 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:102 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:14:52,782 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:134 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:160 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74531_1755004492
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:161 - ✓ 数据隔离: 使用独立的浏览器数据目录
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:168 - 清理函数已注册
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:66 - 环境设置记录完成
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:69 - 环境初始化完成
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:181 - 开始环境清理
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:190 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74531_1755004492
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:204 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:225 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:267 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:14:52,783 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:209 - 环境清理完成
2025-08-12 21:14:52,784 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:181 - 开始环境清理
2025-08-12 21:14:52,784 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:204 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:14:52,784 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:225 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:14:52,784 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:267 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:14:52,784 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:209 - 环境清理完成
2025-08-12 21:15:53,915 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:15:53,916 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:15:53,916 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:15:53,916 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:15:53,958 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-12 21:15:53,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-12 21:15:53,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-12 21:15:53,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-12 21:15:53,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:153 - ✓ PyInstaller环境检测完成
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:154 - 🚀 程序启动 - 网易云音乐下载器 Python版本
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:155 - 📋 开始执行主程序流程，当前阶段: 预初始化
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:164 - 📖 开始加载配置文件...
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:166 - ✓ 配置文件加载完成，开始验证配置有效性
2025-08-12 21:15:53,959 - [34m__main__[0m - [32mINFO[0m - main:171 - 🔍 开始配置验证和依赖项检查...
2025-08-12 21:15:53,959 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-12 21:15:53,961 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:211 - FFmpeg 依赖验证通过
2025-08-12 21:15:53,962 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:245 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-12 21:15:53,962 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-12 21:15:53,962 - [34m__main__[0m - [32mINFO[0m - main:186 - 📦 检查Python依赖包完整性...
2025-08-12 21:15:53,962 - [34m__main__[0m - [32mINFO[0m - main:191 - ✓ 所有Python依赖包检查通过
2025-08-12 21:15:53,962 - [34m__main__[0m - [32mINFO[0m - main:200 - 🏗️ 开始初始化工作环境...
2025-08-12 21:15:53,962 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:51 - 开始环境初始化
2025-08-12 21:15:53,962 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-12 21:15:53,962 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:102 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-12 21:15:53,962 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:134 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:15:53,963 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:160 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74669_1755004553
2025-08-12 21:15:53,963 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:161 - ✓ 数据隔离: 使用独立的浏览器数据目录
2025-08-12 21:15:53,963 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:168 - 清理函数已注册
2025-08-12 21:15:53,963 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:66 - 环境设置记录完成
2025-08-12 21:15:53,963 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:69 - 环境初始化完成
2025-08-12 21:15:53,963 - [34m__main__[0m - [32mINFO[0m - main:209 - ✓ 工作环境初始化成功，所有目录已准备就绪
2025-08-12 21:15:53,963 - [34m__main__[0m - [32mINFO[0m - main:210 - 📁 下载目录、临时目录、浏览器数据目录已创建并配置完成
2025-08-12 21:15:53,963 - [34m__main__[0m - [32mINFO[0m - main:220 - 🎨 显示程序欢迎界面和配置信息...
2025-08-12 21:15:53,963 - [34m__main__[0m - [32mINFO[0m - main:226 - ✓ 程序信息展示完成，用户界面已准备就绪
2025-08-12 21:15:53,967 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:181 - 开始环境清理
2025-08-12 21:15:53,968 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:190 - 已清理浏览器数据目录: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_74669_1755004553
2025-08-12 21:15:53,968 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:204 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:15:53,968 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:225 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:15:53,969 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:267 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:15:53,969 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:209 - 环境清理完成
2025-08-12 21:15:53,969 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:181 - 开始环境清理
2025-08-12 21:15:53,970 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:204 - 已清理临时目录: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-12 21:15:53,970 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:225 - 🧹 检查下载目录中遗留的browser_data目录...
2025-08-12 21:15:53,970 - [34msrc.env_setup[0m - [32mINFO[0m - _cleanup_legacy_files:267 - ✓ 无需清理: 未发现遗留的browser_data目录
2025-08-12 21:15:53,970 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:209 - 环境清理完成
