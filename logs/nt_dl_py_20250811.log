2025-08-11 02:21:03,518 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:21:03,518 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:21:03,518 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:21:03,518 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:21:03,935 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:21:03,936 - [34msrc.config_validator[0m - [32mIN<PERSON>O[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:21:03,938 - [34msrc.config_validator[0m - [31mERROR[0m - add_error:54 - 配置验证错误: 缺失依赖项 'Chrome/Chromium': 请安装Chrome或Chromium浏览器
2025-08-11 02:21:03,938 - [34msrc.config_validator[0m - [31mERROR[0m - validate_all:95 - 配置验证失败，发现 1 个错误
2025-08-11 02:21:03,951 - [34msrc.performance[0m - [32mINFO[0m - start_download:106 - 开始监控下载: https://music.163.com/song?id=123456 (索引: 0)
2025-08-11 02:21:03,951 - [34msrc.performance[0m - [32mINFO[0m - finish_download:126 - 下载完成: https://music.163.com/song?id=123456 - 1.0MB, 0.0s, 6896.6MB/s
2025-08-11 02:21:03,951 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:21:03,951 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:89 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:21:03,951 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:96 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:104 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_c66c2f5a
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:111 - 清理函数已注册
2025-08-11 02:21:03,952 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:191 - 环境设置完成:
2025-08-11 02:21:03,952 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:192 -   音乐目录: /Users/<USER>/Music/NetEase
2025-08-11 02:21:03,952 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:193 -   临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:21:03,952 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:194 -   浏览器数据: /Users/<USER>/Music/NetEase/.browser_data_c66c2f5a
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:70 - 环境初始化完成
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:118 - 开始环境清理
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:124 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_c66c2f5a
2025-08-11 02:21:03,952 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:133 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:21:04,003 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:138 - 环境清理完成
2025-08-11 02:21:04,003 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:118 - 开始环境清理
2025-08-11 02:21:04,004 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:133 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:21:04,004 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:138 - 环境清理完成
2025-08-11 02:21:40,072 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:21:40,072 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:21:40,072 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:21:40,072 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:21:40,142 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:21:40,142 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:21:40,142 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:21:40,142 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:21:40,142 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:21:40,143 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:21:40,143 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:21:40,144 - [34msrc.config_validator[0m - [31mERROR[0m - add_error:54 - 配置验证错误: 缺失依赖项 'Chrome/Chromium': 请安装Chrome或Chromium浏览器
2025-08-11 02:21:40,144 - [34msrc.config_validator[0m - [31mERROR[0m - validate_all:95 - 配置验证失败，发现 1 个错误
2025-08-11 02:22:17,096 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:22:17,096 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:22:17,096 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:22:17,096 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:22:17,126 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:22:17,126 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:22:17,126 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:22:17,126 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:22:17,126 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:22:17,127 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:22:17,127 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:22:17,129 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:22:17,129 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:89 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:96 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:104 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_ae26011e
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:111 - 清理函数已注册
2025-08-11 02:22:17,129 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:191 - 环境设置完成:
2025-08-11 02:22:17,129 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:192 -   音乐目录: /Users/<USER>/Music/NetEase
2025-08-11 02:22:17,129 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:193 -   临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:17,129 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:194 -   浏览器数据: /Users/<USER>/Music/NetEase/.browser_data_ae26011e
2025-08-11 02:22:17,129 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:70 - 环境初始化完成
2025-08-11 02:22:17,130 - [34m__main__[0m - [31mERROR[0m - main:118 - 环境初始化失败: 'Logger' object has no attribute 'log_environment_setup'
2025-08-11 02:22:17,130 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:118 - 开始环境清理
2025-08-11 02:22:17,130 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:124 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_ae26011e
2025-08-11 02:22:17,130 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:133 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:17,130 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:138 - 环境清理完成
2025-08-11 02:22:42,256 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:22:42,256 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:22:42,256 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:22:42,256 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:22:42,285 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:22:42,285 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:22:42,285 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:22:42,285 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:22:42,285 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:22:42,285 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:22:42,286 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:22:42,287 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:22:42,287 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:22:42,287 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:22:42,287 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:90 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:22:42,287 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:97 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:42,287 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:105 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_9d13516b
2025-08-11 02:22:42,287 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:112 - 清理函数已注册
2025-08-11 02:22:42,287 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:191 - 环境设置完成:
2025-08-11 02:22:42,287 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:192 -   音乐目录: /Users/<USER>/Music/NetEase
2025-08-11 02:22:42,288 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:193 -   临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:42,288 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:194 -   浏览器数据: /Users/<USER>/Music/NetEase/.browser_data_9d13516b
2025-08-11 02:22:42,288 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:71 - 环境初始化完成
2025-08-11 02:22:42,288 - [34m__main__[0m - [31mERROR[0m - main:118 - 环境初始化失败: 'Logger' object has no attribute 'log_environment_setup'
2025-08-11 02:22:42,288 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:119 - 开始环境清理
2025-08-11 02:22:42,288 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:125 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_9d13516b
2025-08-11 02:22:42,288 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:134 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:22:42,288 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:139 - 环境清理完成
2025-08-11 02:23:20,818 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:23:20,820 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:23:20,820 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:23:20,820 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:23:20,854 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:23:20,854 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:23:20,854 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:23:20,854 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:23:20,854 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:23:20,855 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:23:20,855 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:23:20,857 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:23:20,857 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:23:20,857 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:23:20,857 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:90 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:23:20,857 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:97 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:23:20,857 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:105 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_0bb0f32b
2025-08-11 02:23:20,857 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:112 - 清理函数已注册
2025-08-11 02:23:20,857 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:191 - 环境设置完成:
2025-08-11 02:23:20,857 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:192 -   音乐目录: /Users/<USER>/Music/NetEase
2025-08-11 02:23:20,858 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:193 -   临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:23:20,858 - [34mdownload[0m - [32mINFO[0m - log_environment_setup:194 -   浏览器数据: /Users/<USER>/Music/NetEase/.browser_data_0bb0f32b
2025-08-11 02:23:20,858 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:71 - 环境初始化完成
2025-08-11 02:23:20,858 - [34m__main__[0m - [31mERROR[0m - main:118 - 环境初始化失败: 'Logger' object has no attribute 'log_environment_setup'
2025-08-11 02:23:20,858 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:119 - 开始环境清理
2025-08-11 02:23:20,858 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:125 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_0bb0f32b
2025-08-11 02:23:20,858 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:134 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:23:20,858 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:139 - 环境清理完成
2025-08-11 02:23:47,898 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:23:47,898 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:23:47,898 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:23:47,898 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:23:47,927 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:23:47,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:23:47,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:23:47,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:23:47,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:23:47,927 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:23:47,928 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:23:47,929 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:23:47,929 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:87 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:94 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:102 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_f0aee268
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:109 - 清理函数已注册
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-11 02:23:47,929 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-11 02:23:47,929 - [34m__main__[0m - [31mERROR[0m - main:118 - 环境初始化失败: 'Logger' object has no attribute 'log_environment_setup'
2025-08-11 02:23:47,930 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:116 - 开始环境清理
2025-08-11 02:23:47,930 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:122 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_f0aee268
2025-08-11 02:23:47,930 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:131 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:23:47,930 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:136 - 环境清理完成
2025-08-11 02:24:19,503 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:24:19,503 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:24:19,503 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:24:19,504 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:24:19,532 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:24:19,532 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:24:19,532 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:24:19,533 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:24:19,533 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:24:19,533 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:24:19,533 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:24:19,535 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:24:19,535 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:87 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:94 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:102 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_7de43016
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:109 - 清理函数已注册
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-11 02:24:19,535 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-11 02:24:19,535 - [34m__main__[0m - [32mINFO[0m - main:111 - 环境初始化成功，所有目录已准备就绪
2025-08-11 02:24:19,538 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:116 - 开始环境清理
2025-08-11 02:24:19,538 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:122 - 已清理浏览器数据目录: /Users/<USER>/Music/NetEase/.browser_data_7de43016
2025-08-11 02:24:19,538 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:131 - 已清理临时目录: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:24:19,538 - [34msrc.env_setup[0m - [32mINFO[0m - cleanup_environment:136 - 环境清理完成
2025-08-11 02:26:04,889 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:26:04,889 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:26:04,889 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:26:04,889 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:26:04,926 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:26:04,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:26:04,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:26:04,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:26:04,927 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:26:04,927 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:26:04,929 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:26:04,930 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:26:04,931 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:87 - 音乐目录设置: /Users/<USER>/Music/NetEase
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:94 - 临时目录设置: /Users/<USER>/Music/NetEase/.temp_downloads
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:102 - 浏览器数据目录设置: /Users/<USER>/Music/NetEase/.browser_data_faa8649c
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:109 - 清理函数已注册
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-11 02:26:04,931 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-11 02:26:04,931 - [34m__main__[0m - [32mINFO[0m - main:112 - 环境初始化成功，所有目录已准备就绪
2025-08-11 02:35:44,969 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:35:44,969 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:35:44,970 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:35:44,970 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:35:45,003 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
2025-08-11 02:35:45,004 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 02:35:45,004 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 02:35:45,004 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 02:35:45,004 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 02:35:45,004 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:75 - 开始配置验证
2025-08-11 02:35:45,005 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:168 - FFmpeg 依赖验证通过
2025-08-11 02:35:45,006 - [34msrc.config_validator[0m - [32mINFO[0m - _validate_dependencies:202 - Chrome/Chromium 依赖验证通过: /Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev
2025-08-11 02:35:45,006 - [34msrc.config_validator[0m - [32mINFO[0m - validate_all:93 - 配置验证通过
2025-08-11 02:35:45,006 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:50 - 开始环境初始化
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:100 - ✓ 使用默认下载目录: /Users/<USER>/Downloads/nt_dl_downloads
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_music_directory:101 - 💡 提示: 下次可通过命令行参数或配置文件指定自定义路径
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_temp_directory:133 - 临时目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.temp_downloads
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - _setup_browser_data_directory:146 - 浏览器数据目录设置: /Users/<USER>/Downloads/nt_dl_downloads/.browser_data_5631_1754850945
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - _register_cleanup:153 - 清理函数已注册
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:65 - 环境设置记录完成
2025-08-11 02:35:45,007 - [34msrc.env_setup[0m - [32mINFO[0m - initialize_environment:68 - 环境初始化完成
2025-08-11 02:35:45,007 - [34m__main__[0m - [32mINFO[0m - main:112 - 环境初始化成功，所有目录已准备就绪
2025-08-11 03:14:13,958 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:148 - 日志系统初始化完成
2025-08-11 03:14:13,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:149 - 日志级别: DEBUG
2025-08-11 03:14:13,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:150 - 文件日志: 启用
2025-08-11 03:14:13,959 - [34msrc.logging_utils[0m - [32mINFO[0m - setup_logging:152 - 日志目录: logs
2025-08-11 03:14:13,991 - [34masyncio[0m - [36mDEBUG[0m - __init__:54 - Using selector: KqueueSelector
