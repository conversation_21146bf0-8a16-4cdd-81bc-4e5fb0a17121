# nt-dl-py 用户指南

## 📋 项目简介

nt-dl-py 是一个功能完整的网易云音乐下载器，基于Rust版本完整迁移到Python并采用现代化模块架构设计。支持单曲、歌单、专辑的高音质下载，具备企业级的稳定性和性能。

### 🎯 核心特性

- **🎵 完整下载功能**: 支持单曲、歌单、专辑的高音质下载
- **🏗️ 模块化架构**: 14个专业模块，职责清晰，易于维护
- **📦 打包支持**: 完整的PyInstaller打包方案，支持独立可执行文件
- **🛡️ 生产就绪**: 完善的错误处理、日志记录和性能监控
- **⚡ 智能优化**: 错误恢复、缓存管理、网络优化等高级功能

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+ 版本
- **FFmpeg**: 音频处理必需组件
- **网络**: 稳定的互联网连接

### 安装与运行

#### 方式一：从源码运行 (适合开发者)

如果您是开发者或希望从源代码运行，请按以下步骤操作：

```bash
# 1. 克隆项目
git clone <repository-url>
cd nt-dl-py

# 2. 安装Python依赖
pip install -r requirements.txt

# 3. 安装Playwright浏览器依赖
playwright install

# 4. 确保FFmpeg已安装并位于系统PATH中
#    - macOS: brew install ffmpeg
#    - Ubuntu: sudo apt install ffmpeg
#    - Windows: 从官网下载并手动添加到PATH

# 5. 运行程序
python main.py
```

#### 方式二：使用预编译的可执行文件 (适合普通用户)

如果您下载的是预编译好的版本（例如从GitHub Releases下载的压缩包），使用方法非常简单：

1.  解压下载的压缩文件。
2.  直接双击运行其中的 `nt-dl-py` (或 `nt-dl-py.exe`) 文件即可。

---

### ⚠️ 关于程序体积的说明

您可能会注意到，解压后的程序文件夹体积较大（通常超过100MB）。**这是完全正常的**，请不要删除其中的任何文件。

- **原因**: 为了让您无需安装任何额外软件即可运行，我们将一个完整的浏览器（Chromium）捆绑在了程序包中。本程序通过驱动这个内置浏览器来完成下载任务。
- **包含内容**: 文件夹中的 `ms-playwright` 目录就是这个内置的浏览器。
- **重要性**: 如果删除了 `ms-playwright` 目录，程序将无法工作。

---

## 📖 使用说明

### 基本使用流程

1. **启动程序**: 运行 `python main.py` 或可执行文件
2. **输入URL**: 粘贴网易云音乐的链接
3. **选择音质**: 
   - 选项1: 超清母带(SVIP) - 需要SVIP会员
   - 选项2: 高解析度无损(VIP) - 推荐选择
4. **等待下载**: 程序自动处理所有歌曲
5. **查看结果**: 文件保存在指定目录中

### 支持的URL格式

- **单曲**: `https://music.163.com/song?id=123456`
- **歌单**: `https://music.163.com/playlist?id=123456`  
- **专辑**: `https://music.163.com/album?id=123456`

### 输出文件特性

下载的FLAC文件包含：
- ✅ 高质量音频（接近CD品质）
- ✅ 嵌入式封面图片
- ✅ 完整歌词信息（LRC格式）
- ✅ 标准音频标签

## ⚙️ 配置选项

### 环境变量配置

```bash
# 启用调试模式
export DEBUG=1

# 自定义下载目录
export NT_DL_MUSIC_DIR=/path/to/music

# 自定义临时目录  
export NT_DL_TEMP_DIR=/path/to/temp
```

### 程序配置

可以在 `src/config.py` 中修改以下配置：

```python
# 并发下载数量 (建议1-5)
CONCURRENT_LIMIT = 2

# 最大重试次数 (建议2-5)
MAX_RETRIES = 2

# 下载超时时间 (秒)
DOWNLOAD_TIMEOUT = 480
```

## 🛠️ 故障排除

### 常见问题解决

#### 1. FFmpeg未找到
```bash
# 检查FFmpeg是否安装
ffmpeg -version

# 安装FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: 下载并添加到PATH
```

#### 2. 浏览器启动失败

**开发环境解决方案**:
```bash
# 重新安装Playwright浏览器
playwright install chromium

# 检查浏览器路径
python -c "from playwright._impl._driver import compute_driver_executable; print(compute_driver_executable())"
```

**打包环境解决方案**:
```bash
# 检查ms-playwright目录是否存在
ls -la dist/netease_downloader/ms-playwright/

# 如果不存在，按照以下步骤复制:
# 1. 查找系统中的Playwright浏览器
find ~ -name "ms-playwright" -type d 2>/dev/null

# 2. 复制到打包目录 (根据操作系统选择)
# macOS/Linux:
cp -r ~/.cache/ms-playwright/* dist/netease_downloader/ms-playwright/
# Windows:
# xcopy "%USERPROFILE%\AppData\Local\ms-playwright\*" "dist\netease_downloader\ms-playwright\" /E /I /Y

# 3. 设置执行权限 (Linux/macOS)
chmod -R +x dist/netease_downloader/ms-playwright/
```

> 💡 **完整的浏览器打包指南**: 请参考 [Playwright Chromium 打包完整指南](PLAYWRIGHT_PACKAGING_GUIDE.md)

#### 3. 下载失败
- 检查网络连接是否稳定
- 验证URL是否有效
- 确认网易云音乐链接格式正确
- 尝试更换网络环境

#### 4. 权限错误
```bash
# 检查目录权限
ls -la ~/Downloads/

# 创建下载目录
mkdir -p ~/Downloads/nt_dl_downloads

# 修改目录权限 (Linux/macOS)
chmod 755 ~/Downloads/nt_dl_downloads
```

#### 5. 打包问题
```bash
# 验证打包准备
python tests/verify_packaging_ready.py

# 检查依赖完整性
python tests/check_features.py

# 运行完整测试
python tests/test_modules.py
```

### 调试模式

启用详细调试信息：

```bash
# 设置调试环境变量
export DEBUG=1

# 运行程序查看详细日志
python main.py

# 查看日志文件
ls ~/.nt_dl_downloads/.logs/
```

## 📊 性能优化

### 网络优化
- 程序自动使用连接池复用HTTP连接
- 智能DNS缓存减少解析时间
- 自适应速度控制避免被限制

### 缓存优化
- 双层缓存系统（内存+磁盘）
- 智能缓存淘汰算法
- 自动过期清理机制

### 错误恢复
- 智能重试机制
- 指数退避算法
- 特定错误恢复策略

## 🔒 安全特性

- **数据隔离**: 独立的浏览器数据目录
- **临时文件**: 自动清理敏感数据
- **权限控制**: 最小权限原则
- **安全日志**: 不记录敏感信息

## 📈 使用技巧

### 批量下载
```python
# 可以连续输入多个URL
# 程序会自动合并处理
```

### 自定义下载目录
```bash
# 方法1: 环境变量
export NT_DL_MUSIC_DIR=/custom/path

# 方法2: 命令行参数
python main.py /custom/download/path
```

### 性能监控
程序会自动显示：
- 下载速度统计
- 成功率统计
- 总耗时统计
- 文件大小统计

## ⚠️ 使用前必读 - 免责声明

### 🎯 项目用途限定
nt-dl-py 是一个**教育技术项目**，设计目的：
- **编程学习**: 学习Python异步编程和模块化设计
- **技术研究**: 研究网络协议和软件架构
- **算法实践**: 实践加密算法和数据处理技术

### 🌐 数据来源透明化
- **公开信息**: 所有数据均来源于公开互联网
- **第三方API**: 使用公开的第三方解析接口
- **公益服务**: 依赖公益性质的音乐解析站点
- **无自有服务**: 本项目不运营任何解析服务

### 🚫 责任免除声明
本项目开发者**明确免除**以下责任：

#### 内容相关责任
- **版权问题**: 不对音乐内容的版权状况负责
- **内容合法性**: 不保证解析内容的合法性
- **内容质量**: 不保证解析内容的质量和完整性
- **内容可用性**: 不保证解析服务的持续可用

#### 技术相关责任
- **服务稳定性**: 第三方解析服务的稳定性
- **数据准确性**: 解析数据的准确性和时效性
- **系统兼容性**: 在不同系统环境下的兼容性
- **安全风险**: 使用过程中可能的安全风险

#### 法律相关责任
- **合规性**: 用户使用行为的合规性
- **法律后果**: 用户可能面临的法律后果
- **纠纷处理**: 因使用本工具产生的任何纠纷
- **损失赔偿**: 因使用本工具造成的任何损失

### 📋 用户义务和责任

#### 必须遵守的义务
1. **法律合规**: 确保使用行为符合当地法律法规
2. **版权尊重**: 尊重音乐作品的知识产权
3. **合理使用**: 仅用于个人学习和技术研究
4. **风险承担**: 承担使用本工具的所有风险

#### 禁止的使用方式
- ❌ 大规模批量下载音乐内容
- ❌ 将下载内容用于商业用途
- ❌ 侵犯音乐作品版权的行为
- ❌ 对第三方服务进行恶意攻击
- ❌ 违反当地法律法规的使用

### 🔒 安全和隐私提醒
- **数据安全**: 用户应自行保护下载数据的安全
- **隐私保护**: 注意保护个人隐私信息
- **网络安全**: 使用过程中注意网络安全防护
- **系统安全**: 确保运行环境的安全性

### ⚖️ 争议解决
- **免责优先**: 开发者享有最大程度的免责保护
- **用户自担**: 用户承担使用过程中的所有风险
- **法律适用**: 争议适用项目开发者所在地法律
- **管辖法院**: 由项目开发者所在地法院管辖

## 📄 使用许可

本项目在以下条件下提供使用许可：
- **学习目的**: 仅限用于编程学习和技术研究
- **非商业性**: 严禁任何形式的商业使用
- **合规使用**: 用户必须确保使用行为合法合规
- **风险自担**: 用户承担所有使用风险和后果

**重要提醒**: 继续使用本项目即表示您已阅读、理解并同意上述所有条款。

## 🆘 获取帮助

- **文档**: 查看项目docs目录下的其他文档
- **问题**: 提交GitHub Issue
- **讨论**: 参与GitHub Discussions

---

**用户指南版本**: v2.2.0  
**最后更新**: 2024年12月29日  
**适用版本**: nt-dl-py v2.2.0+