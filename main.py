#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网易云音乐下载器 - 主程序入口模块
NetEase Cloud Music Downloader - Main Program Entry Module
========================================================

这是网易云音乐下载器的主程序入口文件，负责协调各个模块的工作，实现完整的下载流程。
程序采用现代Python异步编程模式和模块化架构设计，每个功能模块都有明确的职责分工。

This is the main program entry file for the NetEase Cloud Music downloader, responsible for coordinating
the work of various modules and implementing the complete download process. The program adopts modern
Python asynchronous programming patterns and modular architecture design.

核心功能模块 (Core Functional Modules):
=====================================
1. 🔧 环境初始化和配置管理 (Environment initialization and configuration management)
   - 配置文件自动生成和加载 (Automatic configuration file generation and loading)
   - 跨平台目录结构创建 (Cross-platform directory structure creation)
   - 依赖项验证和环境检查 (Dependency validation and environment checking)

2. 🎯 用户交互和输入处理 (User interaction and input processing)
   - 法律声明展示和用户确认 (Legal disclaimer display and user confirmation)
   - URL输入验证和格式化 (URL input validation and formatting)
   - 音质选择和参数配置 (Audio quality selection and parameter configuration)

3. ⚡ 异步下载任务调度 (Asynchronous download task scheduling)
   - 基于asyncio的高效异步下载 (Efficient asynchronous downloading based on asyncio)
   - 智能重试机制和错误恢复 (Intelligent retry mechanism and error recovery)
   - 实时进度监控和性能统计 (Real-time progress monitoring and performance statistics)

4. 📊 结果展示和数据分析 (Result display and data analysis)
   - 彩色终端输出和可视化进度条 (Colored terminal output and visual progress bars)
   - 详细的下载统计和成功率分析 (Detailed download statistics and success rate analysis)
   - 失败任务汇总和错误诊断 (Failed task summary and error diagnosis)

5. 🧹 资源管理和程序清理 (Resource management and program cleanup)
   - 自动临时文件清理 (Automatic temporary file cleanup)
   - 浏览器进程管理和数据隔离 (Browser process management and data isolation)
   - 优雅的程序退出和异常处理 (Graceful program exit and exception handling)

程序执行流程 (Program Execution Flow):
====================================
阶段一: 预初始化 → 阶段二: 环境准备 → 阶段三: 用户交互 → 阶段四: 下载执行 → 阶段五: 结果处理 → 阶段六: 清理退出

技术架构特点 (Technical Architecture Features):
============================================
- **异步编程模式**: 使用asyncio实现高效的并发下载，提升整体性能
- **模块化设计**: 清晰的代码结构和职责分离，便于维护和扩展
- **类型安全**: 全面使用Python类型提示，提高代码可读性和可维护性
- **错误恢复机制**: 多层次的异常处理和自动重试，确保程序稳定性
- **跨平台兼容**: 支持Windows、macOS、Linux等主流操作系统

作者信息 (Author Information):
============================
- 开发团队: nt-dl-py Python开发团队
- 项目版本: v2.0 (Python重构版)
- 技术栈: Python 3.8+, asyncio, Playwright, pycryptodome
- 基于: Rust版本的完整功能对齐

⚠️ 重要法律声明 (Important Legal Statement):
==========================================
本程序仅供编程学习和技术研究使用，严格禁止用于任何商业目的或违法行为。

使用限制 (Usage Restrictions):
- 🎓 仅限个人学习和技术研究 (Personal learning and technical research only)
- 🚫 禁止商业用途和大规模使用 (No commercial use or large-scale usage)
- ⚖️ 必须遵守当地法律法规 (Must comply with local laws and regulations)
- 🛡️ 开发者不承担任何法律责任 (Developers assume no legal responsibility)

数据来源声明 (Data Source Statement):
- 📡 公开互联网资源 (Public internet resources)
- 🌐 第三方公益解析服务 (Third-party public welfare parsing services)
- 📚 公开技术文档和协议 (Public technical documentation and protocols)
"""

import asyncio
import sys
import traceback

# 导入核心功能模块
from src.api import parse_and_route_url                    # URL解析和路由
from src.config import load_config                         # 配置加载
from src.config_validator import validate_config, display_validation_results, provide_solution_suggestions  # 配置验证
from src.downloader import execute_lightweight_download_workflow  # 下载工作流
from src.env_setup import initialize_environment, cleanup_environment  # 环境管理器
from src.logging_utils import setup_logging, get_default_log_config, get_logger  # 日志系统
from src.performance import performance_monitor, display_session_performance_stats  # 性能监控
from src.pyinstaller_compat import setup_pyinstaller_environment  # 打包兼容
from src.ui import display_final_results, display_program_header, select_audio_quality, get_user_url_input, display_legal_notice  # 用户界面
from src.utils import check_dependencies, check_ffmpeg, print_error, print_info, print_warn  # 工具函数


async def main():
    """
    主程序异步入口函数 - 网易云音乐下载器核心控制流程
    Main Program Asynchronous Entry Function - NetEase Cloud Music Downloader Core Control Flow
    ========================================================================================

    这是程序的核心控制流程函数，负责协调各个功能模块的工作，实现完整的下载流程。
    采用现代Python异步编程模式，支持高效的并发下载和优雅的错误处理。

    This is the core control flow function of the program, responsible for coordinating the work
    of various functional modules and implementing the complete download process.

    详细执行流程 (Detailed Execution Flow):
    ====================================
    阶段一: 预初始化 → 阶段二: 环境准备 → 阶段三: 用户交互 → 阶段四: 下载执行 → 阶段五: 结果处理 → 阶段六: 清理退出

    🔧 第一阶段: 预初始化阶段 - PyInstaller环境检测、日志系统初始化、配置文件加载验证
    🏗️ 第二阶段: 环境准备阶段 - 跨平台目录创建、系统依赖检查、遗留文件清理
    🎭 第三阶段: 用户交互阶段 - 程序信息展示、法律声明确认、URL输入验证、音质选择
    🚀 第四阶段: 下载执行阶段 - URL解析任务分发、异步下载执行、智能重试机制、实时进度监控
    📈 第五阶段: 结果处理阶段 - 文件后处理、统计分析、彩色结果展示、用户建议
    🧹 第六阶段: 清理退出阶段 - 临时文件清理、浏览器进程管理、程序状态保存、优雅退出

    异常处理机制 (Exception Handling):
    - 🛡️ 多层次异常捕获和自动错误恢复
    - 📝 详细错误日志记录和用户友好提示
    - 🧹 资源清理保证和优雅程序退出

    性能优化特性 (Performance Features):
    - ⚡ 异步并发下载和智能任务调度
    - 📊 实时性能监控和自适应重试策略
    - 💾 内存使用优化和流式处理

    Returns:
        int: 程序退出状态码 (Program exit status code)
             0 - 正常退出，所有任务成功完成 (Normal exit, all tasks completed successfully)
             1 - 异常退出，发生了不可恢复的错误 (Abnormal exit, unrecoverable error occurred)
             2 - 用户中断退出，用户主动取消程序运行 (User interrupted exit)

    Raises:
        SystemExit: 当程序需要立即退出时抛出 (Raised when program needs immediate exit)
        KeyboardInterrupt: 用户按Ctrl+C中断程序时捕获 (Caught when user presses Ctrl+C)
        Exception: 捕获所有其他未预期的异常 (Catches all other unexpected exceptions)
    """
    # ==================== 🔧 第一阶段: 预初始化阶段 (Phase 1: Pre-initialization) ====================
    # 这个阶段负责程序运行前的基础环境准备，包括打包环境检测、日志系统初始化和配置文件处理
    # This phase is responsible for basic environment preparation before program execution

    # 步骤1.1: PyInstaller打包环境检测和兼容性设置 (Step 1.1: PyInstaller environment detection and compatibility setup)
    # 必须在导入playwright之前执行，检测是否为打包环境并设置相应的浏览器路径
    # Must be executed before importing playwright, detects packaging environment and sets browser paths
    setup_pyinstaller_environment()

    # 步骤1.2: 结构化日志系统初始化 (Step 1.2: Structured logging system initialization)
    # 创建多级别日志记录器，支持控制台彩色输出和文件持久化存储
    # Creates multi-level logger with colored console output and file persistence
    log_config = get_default_log_config()
    setup_logging(log_config)
    logger = get_logger(__name__)
    logger.info("✓ PyInstaller环境检测完成")
    logger.info("🚀 程序启动 - 网易云音乐下载器 Python版本")
    logger.info("📋 开始执行主程序流程，当前阶段: 预初始化")

    # ==================== 🏗️ 第二阶段: 环境准备阶段 (Phase 2: Environment Preparation) ====================
    # 这个阶段负责验证系统环境、加载配置文件、检查依赖项，确保程序具备运行条件
    # This phase validates system environment, loads configuration, checks dependencies

    # 步骤2.1: 配置文件自动加载和验证 (Step 2.1: Automatic configuration loading and validation)
    # 如果配置文件不存在则自动创建默认配置，然后加载并验证配置完整性
    # Automatically creates default config if not exists, then loads and validates configuration integrity
    logger.info("📖 开始加载配置文件...")
    config = load_config()
    logger.info("✓ 配置文件加载完成，开始验证配置有效性")

    # 步骤2.2: 配置完整性和依赖项验证 (Step 2.2: Configuration integrity and dependency validation)
    # 验证配置项的有效性，检查系统依赖（FFmpeg、Chrome等），确保程序能正常运行
    # Validates configuration items, checks system dependencies (FFmpeg, Chrome, etc.)
    logger.info("🔍 开始配置验证和依赖项检查...")
    validation_result = validate_config(config)
    display_validation_results(validation_result)

    # 如果配置验证失败，显示解决建议并退出程序
    # If configuration validation fails, show solution suggestions and exit
    if not validation_result.is_valid:
        logger.error("❌ 配置验证失败，程序无法继续运行")
        provide_solution_suggestions(validation_result)
        print_error("❌ 配置验证失败，程序无法继续运行")
        return 1

    # 步骤2.3: Python依赖包完整性检查 (Step 2.3: Python dependency package integrity check)
    # 检查所有必需的Python第三方包是否正确安装，包括requests、playwright、pycryptodome等
    # Checks if all required Python third-party packages are correctly installed
    logger.info("📦 检查Python依赖包完整性...")
    if not check_dependencies():
        logger.error("❌ Python依赖包检查失败，请检查requirements.txt中的包是否正确安装")
        print_error("❌ 依赖包检查失败，请运行: pip install -r requirements.txt")
        return 1
    logger.info("✓ 所有Python依赖包检查通过")
    
    # ==================== 🎭 第三阶段: 用户交互阶段 (Phase 3: User Interaction) ====================
    # 这个阶段负责创建工作环境、展示程序信息、获取用户输入，为下载任务做准备
    # This phase creates working environment, displays program info, gets user input for download preparation

    # 步骤3.1: 跨平台工作环境初始化 (Step 3.1: Cross-platform working environment initialization)
    # 创建下载目录、临时目录、浏览器数据目录，设置适当的权限和清理机制
    # Creates download directory, temp directory, browser data directory with proper permissions and cleanup
    logger.info("🏗️ 开始初始化工作环境...")
    try:
        if not initialize_environment(config):
            logger.error("❌ 工作环境初始化失败，无法创建必要的目录结构")
            print_error("❌ 环境初始化失败，请检查目录权限")
            return 1

        # 记录环境设置完成状态到日志系统
        # Log environment setup completion status to logging system
        logger.info("✓ 工作环境初始化成功，所有目录已准备就绪")
        logger.info("📁 下载目录、临时目录、浏览器数据目录已创建并配置完成")

    except Exception as e:
        logger.error(f"❌ 环境初始化过程中发生未预期错误: {e}")
        print_error(f"❌ 环境初始化失败: {e}")
        return 1

    # 步骤3.2: 程序信息展示和配置概览 (Step 3.2: Program information display and configuration overview)
    # 向用户展示程序版本、技术信息、配置的目录路径等关键信息
    # Display program version, technical info, configured directory paths and other key information
    logger.info("🎨 显示程序欢迎界面和配置信息...")
    from src.env_setup import get_music_directory, get_current_browser_data_dir
    display_program_header(
        str(get_music_directory()),              # 音乐文件保存路径 (Music file save path)
        str(get_current_browser_data_dir())      # 浏览器数据存储路径 (Browser data storage path)
    )
    logger.info("✓ 程序信息展示完成，用户界面已准备就绪")

    # 显示法律声明和使用条款，确保用户了解合法使用方式
    display_legal_notice()
    
    # ==================== 第四阶段: 外部工具检查 ====================
    
    # 检查FFmpeg是否可用（音频处理必需）
    # FFmpeg用于音频格式转换和元数据嵌入
    if not check_ffmpeg():
        print_error("❌ 严重错误: 未安装FFmpeg或未在系统PATH中找到")
        print_info("💡 在macOS上，您可以通过Homebrew安装: brew install ffmpeg")
        logger.error("FFmpeg检查失败")
        return 1
    
    # ==================== 第五阶段: 用户交互 ====================
    
    # 获取用户输入的网易云音乐URL
    # 使用增强版输入界面，支持输入验证和帮助功能
    raw_url = get_user_url_input()
    logger.info(f"用户输入URL: {raw_url}")
    
    # 记录用户输入到日志（用于调试和统计）
    logger.info(f"用户输入URL: {raw_url}")
    
    # ==================== 第六阶段: URL解析 ====================
    
    # 解析用户输入的URL，获取具体的歌曲列表
    # 这个过程会调用网易云API获取歌单/专辑的详细信息
    try:
        urls_to_process = parse_and_route_url(raw_url)
        logger.info(f"URL解析成功，获得 {len(urls_to_process)} 个歌曲URL")
    except Exception as e:
        print_error(f"❌ URL解析失败: {e}")
        logger.error(f"URL解析失败: {e}")
        return 1
    
    # 验证是否获取到有效的歌曲URL
    if not urls_to_process:
        print_error("❌ 未能获取到有效的URL，程序退出")
        logger.error("未获得有效的URL")
        return 1
    
    # ==================== 第七阶段: 音质选择 ====================
    
    # 让用户选择下载音质
    # 支持超清母带(SVIP)和高解析度无损(VIP)两种音质
    selected_quality = select_audio_quality()
    logger.info(f"用户选择音质: {selected_quality}")
    
    # ==================== 第八阶段: 执行下载 ====================
    
    # 显示下载开始信息
    print_info(f"\n开始处理 {len(urls_to_process)} 个URL")
    logger.info(f"开始下载流程，共 {len(urls_to_process)} 个URL")
    
    # 执行轻量级下载工作流
    # 这是核心的下载逻辑，使用浏览器自动化技术
    from src.env_setup import get_temp_directory, get_current_browser_data_dir
    failed_tasks = await execute_lightweight_download_workflow(
        urls_to_process,                          # 要下载的URL列表
        selected_quality,                         # 用户选择的音质
        get_temp_directory(),                     # 临时文件目录
        get_music_directory(),                    # 最终保存目录
        get_current_browser_data_dir()            # 浏览器数据目录
    )
    
    # ==================== 第九阶段: 后处理和清理 ====================
    
    # 处理临时文件，包括清理和恢复
    print("\n" + "=" * 25 + " 🧹 清理与恢复 " + "=" * 25)
    from src.file_handler import handle_temp_directory_cleanup
    handle_temp_directory_cleanup(get_temp_directory())
    
    # 显示性能统计信息
    # 包括下载速度、成功率、总耗时等
    performance_monitor.display_stats()
    
    # 显示最终下载结果
    # 包括成功数量、失败数量、文件保存位置等
    from src.env_setup import get_music_directory
    display_final_results(urls_to_process, failed_tasks, str(get_music_directory()))

    # 显示性能统计报告
    display_session_performance_stats()

    # ==================== 第十阶段: 会话结束 ====================

    # 记录会话统计信息到日志
    success_count = len(urls_to_process) - len(failed_tasks)
    logger.info(f"下载会话结束: 成功 {success_count}/{len(urls_to_process)}")

    # 执行程序正常退出的清理工作
    # 包括删除临时文件、关闭浏览器进程等
    cleanup_environment()
    
    return 0


if __name__ == "__main__":
    """
    程序入口点
    ===========
    
    这里处理程序的启动和异常捕获:
    - 使用asyncio.run()启动异步主函数
    - 捕获KeyboardInterrupt（Ctrl+C）进行优雅退出
    - 捕获其他异常并记录错误信息
    - 确保在任何情况下都能正确清理资源
    """
    try:
        # 运行异步主函数并获取退出码
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        # 用户按Ctrl+C中断程序
        print_warn("\n用户中断了程序。正在退出。")
        cleanup_environment()
        sys.exit(0)
    except Exception as e:
        # 捕获所有未处理的异常
        print_error(f"程序运行时发生未预期的错误: {e}")
        traceback.print_exc()
        cleanup_environment()
        sys.exit(1)