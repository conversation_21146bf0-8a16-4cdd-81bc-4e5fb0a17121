# -*- coding: utf-8 -*-
"""
PyInstaller兼容性模块
=======================

本模块用于处理PyInstaller打包后的环境兼容性问题，特别是Playwright的浏览器依赖。

核心功能:
- 检测程序是否在打包环境（frozen）中运行。
- 若在打包环境中，则动态设置`PLAYWRIGHT_BROWSERS_PATH`环境变量，
  使其指向与可执行文件同级的`ms-playwright`目录。
  这使得Playwright能够自动找到并使用我们手动捆绑的浏览器，
  而无需用户在目标机器上执行`playwright install`。
"""

import os
import sys
from pathlib import Path

from .utils import print_debug, print_info


def setup_pyinstaller_environment() -> bool:
    """
    检测并设置PyInstaller打包环境。

    如果程序是通过PyInstaller打包的（即'frozen'状态），此函数会执行以下操作：
    1. 获取主可执行文件的父目录作为应用程序的根路径。
    2. 构建指向捆绑的浏览器目录的路径（例如 `dist/main/ms-playwright`）。
    3. 将此路径设置为 `PLAYWRIGHT_BROWSERS_PATH` 环境变量。

    这个操作必须在任何`playwright`模块被导入或使用之前完成。

    Returns:
        bool: 如果是在打包环境中运行，则返回 True，否则返回 False。
    """
    if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
        # 检测到PyInstaller打包环境
        application_path = Path(sys.executable).parent
        browsers_path = application_path / "ms-playwright"

        # 设置环境变量，告知Playwright去哪里寻找浏览器二进制文件
        os.environ['PLAYWRIGHT_BROWSERS_PATH'] = str(browsers_path)

        print_info(f"打包环境已检测到。Playwright浏览器路径已动态设置为: {browsers_path}")
        return True
    else:
        # 标准开发环境
        print_debug("标准开发环境，将使用系统默认的Playwright浏览器缓存。")
        return False


def is_packaged_environment() -> bool:
    """
    检测当前是否在PyInstaller打包环境中运行
    ==========================================

    通过检查sys.frozen属性和_MEIPASS属性来判断程序是否被PyInstaller打包。
    这个函数用于在运行时动态调整程序行为。

    Returns:
        bool: 如果在打包环境中运行返回True，否则返回False

    Technical Details:
        - sys.frozen: PyInstaller设置的标志，表示程序被冻结（打包）
        - sys._MEIPASS: PyInstaller的临时目录路径
        - 两个属性同时存在才确认是打包环境
    """
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


def get_browser_executable_path() -> str:
    """
    获取浏览器可执行文件路径
    ========================

    根据当前运行环境（开发环境或打包环境）返回合适的浏览器路径。
    在打包环境中使用捆绑的浏览器，在开发环境中使用系统安装的浏览器。

    Returns:
        str: 浏览器可执行文件的路径
             - 打包环境: 返回捆绑浏览器的路径
             - 开发环境: 返回空字符串（使用默认路径）

    Note:
        这个函数主要用于Playwright的浏览器启动配置，
        确保在不同环境下都能正确找到浏览器可执行文件。
    """
    if is_packaged_environment():
        # 在打包环境中，浏览器位于应用程序目录下
        application_path = Path(sys.executable).parent
        browser_path = application_path / "ms-playwright" / "chromium"

        # 根据操作系统选择正确的可执行文件
        if sys.platform == "win32":
            browser_executable = browser_path / "chrome.exe"
        elif sys.platform == "darwin":
            browser_executable = browser_path / "Chromium.app" / "Contents" / "MacOS" / "Chromium"
        else:  # Linux
            browser_executable = browser_path / "chrome"

        return str(browser_executable) if browser_executable.exists() else ""
    else:
        # 在开发环境中，使用Playwright默认的浏览器路径
        return ""
