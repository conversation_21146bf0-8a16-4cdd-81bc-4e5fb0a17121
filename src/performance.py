#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块 - Performance Monitoring Module
==========================================

提供下载性能统计和监控功能
Provides download performance statistics and monitoring

功能概述 (Feature Overview):
- 下载统计：记录每次下载的详细信息 (Download statistics)
- 性能分析：计算下载速度、成功率等指标 (Performance analysis)
- 会话管理：提供会话级别的统计报告 (Session management)
- 实时监控：支持实时性能数据展示 (Real-time monitoring)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import time
from datetime import datetime, timezone
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from threading import Lock

from .logging_utils import get_logger
from .utils import print_info, print_success, print_warn

logger = get_logger(__name__)

@dataclass
class DownloadStats:
    """下载统计信息 (Download Statistics Information)"""
    url: str
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None
    file_size: Optional[int] = None
    success: bool = False
    error_message: Optional[str] = None
    song_title: Optional[str] = None
    quality: Optional[str] = None
    
    @property
    def duration_secs(self) -> float:
        """获取下载耗时（秒）(Get download duration in seconds)"""
        end_time = self.end_time or datetime.now(timezone.utc)
        return (end_time - self.start_time).total_seconds()
    
    @property
    def speed_mbps(self) -> float:
        """获取下载速度（MB/s）(Get download speed in MB/s)"""
        if self.file_size and self.duration_secs > 0:
            return (self.file_size / 1024 / 1024) / self.duration_secs
        return 0.0
    
    def finish(self, success: bool, file_size: Optional[int] = None, error_message: Optional[str] = None):
        """完成下载统计 (Complete download statistics)"""
        self.end_time = datetime.now(timezone.utc)
        self.success = success
        if file_size is not None:
            self.file_size = file_size
        if error_message is not None:
            self.error_message = error_message
    
    def set_song_info(self, title: Optional[str] = None, quality: Optional[str] = None):
        """设置歌曲信息 (Set song information)"""
        if title is not None:
            self.song_title = title
        if quality is not None:
            self.quality = quality

@dataclass
class SessionStats:
    """会话统计信息 (Session Statistics Information)"""
    total_downloads: int = 0
    successful_downloads: int = 0
    failed_downloads: int = 0
    success_rate: float = 0.0
    total_size_mb: float = 0.0
    total_duration_secs: float = 0.0
    avg_speed_mbps: float = 0.0
    session_duration_secs: float = 0.0
    max_speed_mbps: float = 0.0
    min_speed_mbps: float = 0.0

class PerformanceMonitor:
    """
    性能监控器 (Performance Monitor)
    ==============================

    基于Rust版本的PerformanceMonitor设计，提供完整的性能监控和统计功能。
    对应Rust版本中的性能监控系统，包含下载统计、会话管理和资源监控。

    Features:
    - 下载统计：记录每次下载的详细信息
    - 性能分析：计算下载速度、成功率等指标
    - 会话管理：提供会话级别的统计报告
    - 线程安全：支持多线程环境下的安全操作
    """

    def __init__(self):
        self.stats: List[DownloadStats] = []
        self.session_start = time.time()
        self.session_start_dt = datetime.now(timezone.utc)
        self._lock = Lock()

        # 添加会话统计缓存 - 对应Rust版本的性能优化
        self._cached_session_stats: Optional[SessionStats] = None
        self._stats_dirty = False
    
    def start_download(self, url: str) -> int:
        """
        开始下载监控 (Start download monitoring)
        ====================================

        基于Rust版本的下载监控设计，记录下载开始时间和URL信息。
        对应Rust版本中的性能监控开始记录功能。

        Args:
            url: 要监控的下载URL

        Returns:
            int: 监控索引，用于后续的完成记录
        """
        with self._lock:
            download_stat = DownloadStats(url=url)
            self.stats.append(download_stat)
            index = len(self.stats) - 1

            # 标记统计数据已更新
            self._stats_dirty = True
            self._cached_session_stats = None

            logger.info(f"开始监控下载: {url} (索引: {index})")
            return index
    
    def finish_download(
        self,
        index: int,
        success: bool,
        file_size: Optional[int] = None,
        error_message: Optional[str] = None,
        song_title: Optional[str] = None,
        quality: Optional[str] = None,
    ):
        """
        完成下载监控 (Complete download monitoring)
        ======================================

        基于Rust版本的下载完成记录设计，更新下载统计信息。
        对应Rust版本中的性能监控完成记录功能。

        Args:
            index: 监控索引
            success: 是否成功
            file_size: 文件大小（字节）
            error_message: 错误信息
            song_title: 歌曲标题
            quality: 音质信息
        """
        with self._lock:
            if 0 <= index < len(self.stats):
                stat = self.stats[index]
                stat.finish(success, file_size, error_message)
                stat.set_song_info(song_title, quality)

                # 标记统计数据已更新
                self._stats_dirty = True
                self._cached_session_stats = None

                if success and file_size:
                    logger.info(
                        f"下载完成: {stat.url} - "
                        f"{stat.file_size / 1024 / 1024:.1f}MB, "
                        f"{stat.duration_secs:.1f}s, "
                        f"{stat.speed_mbps:.1f}MB/s"
                    )
                else:
                    logger.warning(
                        f"下载失败: {stat.url} - "
                        f"{error_message or '未知错误'}"
                    )
    
    def get_session_stats(self) -> SessionStats:
        """
        获取会话统计信息 (Get session statistics)
        ====================================

        基于Rust版本的会话统计设计，提供完整的性能指标计算。
        对应Rust版本中的性能统计功能，包含缓存优化。

        Returns:
            SessionStats: 包含所有性能指标的统计对象
        """
        with self._lock:
            # 如果统计数据未更新且有缓存，直接返回缓存
            if not self._stats_dirty and self._cached_session_stats:
                return self._cached_session_stats

            # 计算基础统计
            total_downloads = len(self.stats)
            successful_downloads = sum(1 for s in self.stats if s.success)
            failed_downloads = total_downloads - successful_downloads

            # 计算大小和时间统计
            total_size = sum(s.file_size or 0 for s in self.stats if s.success)
            total_duration = sum(s.duration_secs for s in self.stats if s.end_time)

            # 计算平均速度
            avg_speed = 0.0
            if total_duration > 0 and total_size > 0:
                avg_speed = (total_size / 1024 / 1024) / total_duration

            # 计算速度范围
            speeds = [s.speed_mbps for s in self.stats if s.success and s.file_size]
            max_speed = max(speeds) if speeds else 0.0
            min_speed = min(speeds) if speeds else 0.0

            # 计算会话时长
            session_duration = time.time() - self.session_start

            # 创建统计对象
            stats = SessionStats(
                total_downloads=total_downloads,
                successful_downloads=successful_downloads,
                failed_downloads=failed_downloads,
                success_rate=successful_downloads / total_downloads if total_downloads > 0 else 0.0,
                total_size_mb=total_size / 1024 / 1024,
                total_duration_secs=total_duration,
                avg_speed_mbps=avg_speed,
                session_duration_secs=session_duration,
                max_speed_mbps=max_speed,
                min_speed_mbps=min_speed,
            )

            # 更新缓存
            self._cached_session_stats = stats
            self._stats_dirty = False

            return stats
    
    def get_all_stats(self) -> List[DownloadStats]:
        """获取所有下载统计 (Get all download statistics)"""
        with self._lock:
            return self.stats.copy()
    
    def get_failed_downloads(self) -> List[DownloadStats]:
        """获取失败的下载 (Get failed downloads)"""
        with self._lock:
            return [s for s in self.stats if not s.success]
    
    def clear_stats(self):
        """清除统计数据 (Clear statistics data)"""
        with self._lock:
            self.stats.clear()
            self._cached_session_stats = None
            self._stats_dirty = False
            logger.info("性能监控统计数据已清除")

    def display_stats(self):
        """
        显示实时性能统计 (Display real-time performance statistics)
        ======================================================

        基于Rust版本的实时性能监控设计，提供当前会话的性能概览。
        对应Rust版本中的性能监控显示功能。
        """
        stats = self.get_session_stats()

        if stats.total_downloads == 0:
            print_info("📊 暂无下载统计数据")
            return

        print_info("📊 实时性能统计:")
        print(f"   • 当前会话: {stats.total_downloads} 个任务")
        print(f"   • 成功率: {stats.success_rate * 100:.1f}%")

        if stats.successful_downloads > 0:
            print(f"   • 平均速度: {stats.avg_speed_mbps:.1f} MB/s")
            print(f"   • 总下载量: {stats.total_size_mb:.1f} MB")

        if stats.failed_downloads > 0:
            print(f"   • 失败任务: {stats.failed_downloads} 个")

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要 (Get performance summary)
        =================================

        返回性能统计的字典格式，便于其他模块使用。

        Returns:
            Dict[str, Any]: 性能统计摘要
        """
        stats = self.get_session_stats()
        return {
            "total_downloads": stats.total_downloads,
            "successful_downloads": stats.successful_downloads,
            "failed_downloads": stats.failed_downloads,
            "success_rate": stats.success_rate,
            "avg_speed_mbps": stats.avg_speed_mbps,
            "total_size_mb": stats.total_size_mb,
            "session_duration_secs": stats.session_duration_secs
        }

# 全局性能监控器实例 (Global performance monitor instance)
performance_monitor = PerformanceMonitor()

def display_session_performance_stats():
    """
    显示会话性能统计 (Display session performance statistics)
    ===================================================

    基于Rust版本的性能统计报告设计，提供完整的会话性能分析。
    对应Rust版本中的性能监控和统计展示功能。
    """
    stats = performance_monitor.get_session_stats()

    print("\n" + "=" * 60)
    print_success("📊 会话性能统计报告")
    print("=" * 60)

    # 总体统计 - 对应Rust版本的基础统计信息
    print_info("📈 总体统计:")
    print(f"   • 总下载数: {stats.total_downloads}")
    print(f"   • 成功下载: {stats.successful_downloads} 首")
    print(f"   • 失败下载: {stats.failed_downloads} 首")
    print(f"   • 成功率: {stats.success_rate * 100:.1f}%")

    # 性能指标 - 对应Rust版本的性能监控指标
    if stats.successful_downloads > 0:
        print_info("🚀 性能指标:")
        print(f"   • 总下载量: {stats.total_size_mb:.1f} MB")
        print(f"   • 平均速度: {stats.avg_speed_mbps:.1f} MB/s")
        print(f"   • 最快速度: {stats.max_speed_mbps:.1f} MB/s")
        print(f"   • 最慢速度: {stats.min_speed_mbps:.1f} MB/s")
        print(f"   • 总下载时间: {stats.total_duration_secs:.1f} 秒")

    # 会话信息 - 对应Rust版本的会话管理
    print_info("⏱️  会话信息:")
    print(f"   • 会话时长: {stats.session_duration_secs:.1f} 秒")

    # 失败详情 - 对应Rust版本的错误统计
    if stats.failed_downloads > 0:
        print_warn("❌ 失败详情:")
        failed_downloads = performance_monitor.get_failed_downloads()
        for i, failed in enumerate(failed_downloads[:5], 1):
            print(f"   {i}. {failed.url} - {failed.error_message or '未知错误'}")
        if len(failed_downloads) > 5:
            print(f"   ... 还有 {len(failed_downloads) - 5} 个失败项目")

    print("=" * 60)
