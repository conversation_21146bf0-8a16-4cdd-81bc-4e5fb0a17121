# -*- coding: utf-8 -*-
"""
API模块 - 网易云音乐API请求和URL解析
===================================

这个模块负责处理与网易云音乐API的所有交互，包括：
1. URL解析和路由：识别单曲、歌单、专辑URL
2. API请求：调用网易云官方API获取数据
3. 数据解析：处理API返回的JSON数据
4. 错误处理：统一的异常处理和错误恢复

支持的URL格式:
- 单曲: https://music.163.com/song?id=123456
- 歌单: https://music.163.com/playlist?id=123456
- 专辑: https://music.163.com/album?id=123456

技术实现:
- 使用正则表达式进行URL模式匹配
- 调用weapi加密接口保护API请求
- 使用requests库进行HTTP通信
- 统一的错误处理和日志记录

作者: nt_dl_team
版本: v2.2.0
基于: 网易云音乐官方API协议

⚠️ API使用声明:
本模块仅用于技术学习和研究目的。
- API调用: 基于公开的网络协议规范
- 数据获取: 通过官方公开接口实现
- 加密算法: 使用公开的技术文档
- 使用限制: 仅供学习研究，禁止商业用途
- 免责声明: API使用风险由用户自行承担
"""

import json
import re
from typing import List

import requests
from bs4 import BeautifulSoup

from .config import HEADERS
from .crypto import get_weapi_params
from .utils import print_info, print_success, print_warn, ProcessingError


def parse_and_route_url(raw_url: str) -> List[str]:
    """
    解析和路由网易云音乐URL - 主要入口函数
    ========================================

    这是API模块的主要入口函数，负责解析用户输入的网易云音乐URL，
    识别其类型（单曲、歌单、专辑），并返回对应的歌曲URL列表。

    URL解析流程:
    1. 清理URL：移除多余参数和空白字符
    2. 模式匹配：使用正则表达式识别URL类型
    3. 路由分发：根据类型调用相应的处理函数
    4. 数据获取：调用API获取歌曲列表
    5. 结果返回：统一格式的歌曲URL列表

    支持的URL格式:
    - 单曲: https://music.163.com/song?id=123456
    - 歌单: https://music.163.com/playlist?id=123456
    - 专辑: https://music.163.com/album?id=123456
    - 移动端: https://music.163.com/#/song?id=123456

    Args:
        raw_url (str): 用户输入的原始URL字符串
                      可能包含多余的参数和空白字符

    Returns:
        List[str]: 歌曲URL列表，每个URL格式为:
                  "https://music.163.com/song?id={song_id}"

    Raises:
        ProcessingError: URL解析失败时抛出，包含详细错误信息
                        - URL格式不匹配
                        - API请求失败
                        - 数据解析错误

    Example:
        >>> urls = parse_and_route_url("https://music.163.com/playlist?id=123456")
        >>> len(urls) > 0
        True
        >>> urls[0].startswith("https://music.163.com/song?id=")
        True

    Technical Details:
        - 使用正则表达式进行高效的URL模式匹配
        - 支持网易云音乐的多种URL格式变体
        - 自动处理移动端和桌面端URL差异
        - 统一的错误处理和日志记录
    """
    cleaned_url = raw_url.strip().split('&')[0]
    print_info(f"正在解析URL: {cleaned_url}")
    
    # 匹配歌单或专辑
    playlist_album_match = re.search(r'music\.163\.com/(?:#/)?(album|playlist)\?id=(\d+)', cleaned_url)
    if playlist_album_match:
        url_type, item_id = playlist_album_match.groups()
        try:
            if url_type == 'playlist':
                return fetch_playlist_data(item_id)
            elif url_type == 'album':
                return fetch_album_data(item_id)
        except Exception as e:
            raise ProcessingError(f"无法从 {url_type} ID {item_id} 获取歌曲列表: {e}")

    # 匹配单曲
    song_match = re.search(r'music\.163\.com/(?:#/)?song\?id=(\d+)', cleaned_url)
    if song_match:
        song_id = song_match.groups()[0]
        print_info("✓ 识别为单曲URL")
        return [f"https://music.163.com/song?id={song_id}"]

    raise ProcessingError(f"URL格式不匹配任何有效的单曲、专辑或歌单: {raw_url}")


def fetch_playlist_data(playlist_id: str) -> List[str]:
    """
    获取歌单数据
    
    Args:
        playlist_id: 歌单ID
        
    Returns:
        歌曲URL列表
    """
    print_info("✓ 识别为歌单，正在获取歌曲列表...")
    
    api_url = "https://music.163.com/weapi/v6/playlist/detail?csrf_token="
    payload = {
        "id": playlist_id,
        "n": 100000,
        "s": 8,
        "csrf_token": ""
    }
    
    encrypted_data = get_weapi_params(payload)
    
    try:
        response = requests.post(api_url, headers=HEADERS, data=encrypted_data, timeout=10)
        response.raise_for_status()
        result = response.json()
        
        playlist_name = result.get("playlist", {}).get("name", "Unknown_Playlist")
        track_ids = result.get("playlist", {}).get("trackIds", [])
        
        if not track_ids:
            print_warn("警告: 未在歌单中找到任何歌曲")
            return []
        
        song_ids = [str(item['id']) for item in track_ids]
        print_success(f"成功: 在歌单《{playlist_name}》中找到 {len(song_ids)} 首歌曲")
        
        return [f"https://music.163.com/song?id={song_id}" for song_id in song_ids]
        
    except requests.RequestException as e:
        raise ProcessingError(f"获取歌单数据失败: {e}")
    except (KeyError, ValueError) as e:
        raise ProcessingError(f"解析歌单数据失败: {e}")


def fetch_album_data(album_id: str) -> List[str]:
    """
    获取专辑数据
    
    Args:
        album_id: 专辑ID
        
    Returns:
        歌曲URL列表
    """
    print_info("✓ 识别为专辑，正在获取歌曲列表...")
    
    album_url = f"https://music.163.com/album?id={album_id}"
    
    try:
        response = requests.get(album_url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 获取专辑名称
        album_name_elem = soup.find('h2', class_='f-ff2')
        album_name = album_name_elem.text.strip() if album_name_elem else "Unknown_Album"
        
        # 获取歌曲列表
        textarea = soup.find('textarea', id='song-list-pre-data')
        if not textarea or not textarea.string:
            raise ProcessingError("无法在专辑页面中找到歌曲列表")
        
        tracks = json.loads(textarea.string)
        song_ids = [str(track['id']) for track in tracks]
        
        print_success(f"成功: 在专辑《{album_name}》中找到 {len(song_ids)} 首歌曲")
        
        return [f"https://music.163.com/song?id={song_id}" for song_id in song_ids]
        
    except requests.RequestException as e:
        raise ProcessingError(f"获取专辑数据失败: {e}")
    except (json.JSONDecodeError, KeyError, ValueError) as e:
        raise ProcessingError(f"解析专辑数据失败: {e}")