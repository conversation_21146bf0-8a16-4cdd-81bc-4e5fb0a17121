#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证模块 - Configuration Validation Module
============================================

提供启动时配置验证、自动修复、错误报告功能
Provides startup configuration validation, auto-repair, error reporting

功能概述 (Feature Overview):
- 配置完整性检查：验证所有必需的配置项 (Configuration integrity check)
- 依赖项验证：检查外部依赖的可用性 (Dependency validation)
- 自动修复：自动修复常见的配置问题 (Auto-repair)
- 错误报告：提供详细的错误信息和解决建议 (Error reporting)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import os
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from .logging_utils import get_logger
from .utils import print_info, print_warn, print_error, print_success

logger = get_logger(__name__)

@dataclass
class ValidationResult:
    """配置验证结果 (Configuration validation result)"""
    is_valid: bool = True
    errors: List[str] = None
    warnings: List[str] = None
    auto_fixed_count: int = 0
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
    
    def add_error(self, error: str):
        """添加错误 (Add error)"""
        self.is_valid = False
        self.errors.append(error)
        logger.error(f"配置验证错误: {error}")
    
    def add_warning(self, warning: str):
        """添加警告 (Add warning)"""
        self.warnings.append(warning)
        logger.warning(f"配置验证警告: {warning}")
    
    def record_auto_fix(self):
        """记录自动修复 (Record auto-fix)"""
        self.auto_fixed_count += 1
        logger.info("记录一次自动修复")

class ConfigValidator:
    """配置验证器 (Configuration validator)"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.result = ValidationResult()
    
    def validate_all(self) -> ValidationResult:
        """执行完整的配置验证 (Perform complete configuration validation)"""
        logger.info("开始配置验证")
        
        # 验证基本配置
        self._validate_basic_config()
        
        # 验证网络配置
        self._validate_network_config()
        
        # 验证文件路径配置
        self._validate_file_paths()
        
        # 验证外部依赖
        self._validate_dependencies()
        
        # 验证浏览器配置
        self._validate_browser_config()
        
        if self.result.is_valid:
            logger.info("配置验证通过")
        else:
            logger.error(f"配置验证失败，发现 {len(self.result.errors)} 个错误")
        
        if self.result.warnings:
            logger.warning(f"配置验证发现 {len(self.result.warnings)} 个警告")
        
        if self.result.auto_fixed_count > 0:
            logger.info(f"自动修复了 {self.result.auto_fixed_count} 个配置问题")
        
        return self.result
    
    def _validate_basic_config(self):
        """验证基本配置 (Validate basic configuration)"""
        # 验证重试次数
        max_retries = self.config.get('max_retries', 2)
        if max_retries == 0:
            self.result.add_warning("最大重试次数为0，可能导致下载失败时无法重试")
        elif max_retries > 10:
            self.result.add_warning("最大重试次数过高，可能导致程序长时间等待")
        
        # 验证超时设置
        timeout = self.config.get('download_timeout_seconds', 480)
        if timeout < 30:
            self.result.add_warning("下载超时时间过短，可能导致大文件下载失败")
        elif timeout > 1800:
            self.result.add_warning("下载超时时间过长，可能导致程序长时间等待")
        
        # 验证用户代理
        user_agent = self.config.get('user_agent', '')
        if not user_agent:
            self.result.add_error("缺失必需配置项: user_agent")
        elif 'Mozilla' not in user_agent:
            self.result.add_warning("用户代理字符串可能不够真实，建议使用标准浏览器UA")
    
    def _validate_network_config(self):
        """验证网络配置 (Validate network configuration)"""
        # 验证目标URL（使用常量）
        target_url = "https://api.toubiec.cn/wyapi/Song.html"
        if not target_url.startswith("http"):
            self.result.add_error(f"无效的URL: {target_url}")
        
        # 验证网络连接（可选，避免启动时网络检查过慢）
        if "localhost" in target_url or "127.0.0.1" in target_url:
            self.result.add_warning("目标URL指向本地地址，请确认这是预期的配置")
    
    def _validate_file_paths(self):
        """验证文件路径配置 (Validate file path configuration)"""
        # 验证音乐目录
        custom_dir = self.config.get('custom_download_directory', '')
        if custom_dir:
            music_dir = Path(custom_dir)
        else:
            music_dir = Path.home() / "Music" / "NetEase"
        
        if not music_dir.exists():
            try:
                music_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"自动创建音乐目录: {music_dir}")
                self.result.record_auto_fix()
            except Exception as e:
                self.result.add_error(f"无法创建音乐目录 {music_dir}: {e}")
        else:
            # 检查写权限
            test_file = music_dir / ".write_test"
            try:
                test_file.write_text("test")
                test_file.unlink()
            except Exception:
                self.result.add_error(f"音乐目录权限不足: {music_dir}")
    
    def _validate_dependencies(self):
        """验证外部依赖 (Validate external dependencies)"""
        # 验证FFmpeg
        if shutil.which("ffmpeg"):
            logger.info("FFmpeg 依赖验证通过")
        else:
            self.result.add_error("缺失依赖项 'FFmpeg': 请安装FFmpeg并确保其在PATH中可用")
        
        # 验证Chrome/Chromium - 支持不同平台的命令名称和路径
        import platform
        system = platform.system()

        if system == "Darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev",
                "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta",
                "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
            ]
            chrome_commands = ["google-chrome", "chromium", "chrome"]
        elif system == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            ]
            chrome_commands = ["chrome.exe", "google-chrome.exe", "chromium.exe"]
        else:  # Linux
            chrome_paths = []
            chrome_commands = ["google-chrome", "google-chrome-stable", "chromium", "chromium-browser", "chrome"]

        chrome_found = False

        # 检查绝对路径
        for path in chrome_paths:
            if Path(path).exists():
                chrome_found = True
                logger.info(f"Chrome/Chromium 依赖验证通过: {path}")
                break

        # 检查命令
        if not chrome_found:
            for cmd in chrome_commands:
                if shutil.which(cmd):
                    chrome_found = True
                    logger.info(f"Chrome/Chromium 依赖验证通过: {cmd}")
                    break

        if not chrome_found:
            if system in ["Darwin", "Windows"]:
                suggestion = "请从 https://www.google.com/chrome/ 下载安装Chrome浏览器"
            else:
                suggestion = "请安装Chrome或Chromium浏览器: sudo apt install google-chrome-stable 或 sudo apt install chromium-browser"

            self.result.add_error(f"缺失依赖项 'Chrome/Chromium': {suggestion}")
    
    def _validate_browser_config(self):
        """验证浏览器配置 (Validate browser configuration)"""
        # 验证浏览器可见性设置
        if self.config.get('browser_visible', False):
            self.result.add_warning("浏览器可见模式已启用，这可能影响性能")
        
        # 验证并发下载限制
        if self.config.get('max_parallel_downloads', 1) > 1:
            self.result.add_warning("并发下载已启用，当前版本建议使用单线程下载以确保稳定性")

def validate_config(config: Dict[str, Any]) -> ValidationResult:
    """验证配置的便捷函数 (Convenience function for config validation)"""
    validator = ConfigValidator(config)
    return validator.validate_all()

def display_validation_results(result: ValidationResult):
    """显示验证结果 (Display validation results)"""
    if result.is_valid:
        print_success("✅ 配置验证通过")
    else:
        print_error("❌ 配置验证失败")
    
    # 显示错误
    if result.errors:
        print_error("\n🚨 发现以下错误:")
        for i, error in enumerate(result.errors, 1):
            print(f"   {i}. {error}")
    
    # 显示警告
    if result.warnings:
        print_warn("\n⚠️  发现以下警告:")
        for i, warning in enumerate(result.warnings, 1):
            print(f"   {i}. {warning}")
    
    # 显示自动修复信息
    if result.auto_fixed_count > 0:
        print_info(f"\n🔧 自动修复了 {result.auto_fixed_count} 个问题")
    
    print()

def provide_solution_suggestions(result: ValidationResult):
    """提供解决建议 (Provide solution suggestions)"""
    if not result.errors:
        return
    
    print_info("💡 解决建议:")
    
    for error in result.errors:
        if "FFmpeg" in error:
            print("   • FFmpeg: 请访问 https://ffmpeg.org/download.html 下载安装")
        elif "Chrome" in error:
            print("   • Chrome/Chromium: 请安装Chrome或Chromium浏览器")
        elif "目录" in error:
            print("   • 目录权限: 请检查目录权限或使用管理员权限运行")
        elif "user_agent" in error:
            print("   • 用户代理: 请检查配置文件中的user_agent设置")
        else:
            print("   • 检查配置文件: config.json")
    
    print()
