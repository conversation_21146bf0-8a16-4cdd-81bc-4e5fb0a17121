#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置系统模块 - 网易云音乐下载器配置管理
Configuration System Module - NetEase Cloud Music Downloader Configuration Management

功能概述 (Feature Overview):
- JSON格式配置文件支持 (JSON format configuration file support)
- 跨平台配置文件路径管理 (Cross-platform config file path management)
- 运行时配置热更新 (Runtime configuration hot updates)
- 默认配置自动生成 (Automatic default configuration generation)
- 全局配置单例模式 (Global configuration singleton pattern)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

# ==================== 下载行为配置 ====================

# 并发下载任务数限制
# 控制同时进行的下载任务数量，避免对服务器造成过大压力
# 建议值: 1-5，过高可能导致请求被限制或系统资源不足
CONCURRENT_LIMIT = 2

# 最大重试次数
# 当下载失败时的重试次数，包括网络错误、超时等情况
# 建议值: 2-5，过高可能导致程序长时间卡住
MAX_RETRIES = 2

# 下载超时时间（秒）
# 单个下载任务的最大等待时间，超过此时间将被视为失败
# 480秒 = 8分钟，适合大部分网络环境和文件大小
DOWNLOAD_TIMEOUT = 480

# 目标解析网站URL
# 用于解析网易云音乐链接的第三方API服务
# 这个服务提供了网易云音乐的解析接口
TARGET_PAGE_URL = "https://api.toubiec.cn/wyapi/Song.html"

# ==================== 网易云API加密配置 ====================

# RSA加密模数 (Modulus)
# 这是网易云音乐API使用的RSA公钥的模数部分
# 用于weapi接口的双层加密算法（AES + RSA）
# 注意: 这个值是从网易云官方获取的，不应随意修改
MODULUS = (
    "00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7"
)

# AES加密的固定nonce值
# 用于AES加密的初始化向量，这是网易云API的固定值
NONCE = "0CoJUm6Qyw8W8jud"

# RSA公钥指数
# RSA加密算法的公钥指数部分，通常为65537（十六进制0x010001）
PUBKEY = "010001"

# ==================== HTTP请求配置 ====================

# HTTP请求头配置
# 模拟真实浏览器的请求头，避免被服务器识别为爬虫
HEADERS = {
    # 用户代理字符串，模拟Chrome浏览器
    # 使用较新的Chrome版本号以获得更好的兼容性
    'User-Agent': (
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
        '(KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
    ),
    
    # 引用页面，表明请求来源于网易云音乐官网
    'Referer': 'https://music.163.com/',
    
    # 内容类型，用于POST请求的表单数据
    'Content-Type': 'application/x-www-form-urlencoded',
    
    # 接受的语言，优先中文
    'Accept-Language': 'zh-CN,zh;q=0.9',
}

# ==================== 文件格式配置 ====================

# 支持的音频文件扩展名集合
# 用于识别和处理各种音频格式文件
# 包含了常见的无损和有损音频格式
AUDIO_EXTENSIONS = {
    ".wav",   # 无损音频格式，未压缩
    ".mp3",   # 有损压缩格式，最常见
    ".m4a",   # Apple的音频格式，通常为AAC编码
    ".flac",  # 无损压缩格式，高音质
    ".aac",   # 高效音频编码，有损压缩
    ".ogg",   # 开源音频格式，通常为Vorbis编码
    ".opus",  # 现代音频编码，低延迟高质量
    ".wma",   # Windows Media Audio格式
    ".aiff",  # Apple的无损音频格式
    ".alac"   # Apple无损音频编解码器
}

# ==================== 配置管理系统 (Configuration Management System) ====================

# 全局配置实例 (Global configuration instance)
_config_instance: Optional[Dict[str, Any]] = None

def get_config_file_path() -> Path:
    """获取配置文件路径 (Get configuration file path)"""
    # 在程序所在目录创建config.json
    return Path.cwd() / "config.json"

def create_default_config() -> Dict[str, Any]:
    """创建默认配置 (Create default configuration)"""
    return {
        # 浏览器可见性模式：False=隐藏(性能优先)，True=可见(调试用)
        # Browser visibility mode: False for headless (performance), True for visible (debugging)
        "browser_visible": False,

        # 最大并行下载数量（当前锁定为1以确保稳定性）
        # Maximum number of parallel downloads (currently locked to 1 for stability)
        "max_parallel_downloads": 1,

        # 下载失败时的最大重试次数
        # Maximum number of retry attempts for failed downloads
        "max_retries": 2,

        # 下载超时时间（秒）
        # Download timeout in seconds
        "download_timeout_seconds": 480,

        # 浏览器请求的用户代理字符串
        # User agent string for browser requests
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

        # 默认音质偏好设置
        # Default audio quality preference
        "default_audio_quality": "标准",

        # 启用详细调试日志输出
        # Enable detailed logging for debugging
        "enable_debug_logging": False,

        # 自定义下载目录（空字符串表示使用默认目录）
        # Custom download directory (empty string means use default)
        "custom_download_directory": ""
    }

def save_config(config: Dict[str, Any]) -> bool:
    """保存配置到文件 (Save configuration to file)"""
    try:
        config_path = get_config_file_path()
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print(f"📝 配置文件已保存: {config_path}")
        return True
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")
        return False

def load_config() -> Dict[str, Any]:
    """
    加载配置信息 (Load configuration information)
    ============

    从配置文件加载配置，如果文件不存在则创建默认配置。
    Load configuration from file, create default if file doesn't exist.

    Returns:
        dict: 包含所有配置项的字典 (Dictionary containing all configuration items)
    """
    global _config_instance

    if _config_instance is not None:
        return _config_instance

    config_path = get_config_file_path()

    # 如果配置文件存在，尝试加载
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 验证配置完整性，添加缺失的键
            default_config = create_default_config()
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value

            _config_instance = config
            print(f"📖 配置文件加载成功: {config_path}")
            return config

        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")

    # 创建默认配置
    config = create_default_config()
    _config_instance = config

    # 保存默认配置到文件
    save_config(config)

    return config

def get_config() -> Dict[str, Any]:
    """获取当前配置 (Get current configuration)"""
    return load_config()

def update_config(key: str, value: Any) -> bool:
    """更新配置项 (Update configuration item)"""
    global _config_instance

    config = load_config()
    config[key] = value
    _config_instance = config

    return save_config(config)