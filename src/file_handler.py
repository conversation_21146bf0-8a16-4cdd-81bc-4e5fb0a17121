#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理模块 - File Processing Module
==================================

提供下载文件的处理、解压、移动和清理功能
Provides processing, extraction, moving and cleanup of downloaded files

功能概述 (Feature Overview):
- 文件解压：处理ZIP格式的下载文件 (File extraction: process ZIP format downloads)
- 文件移动：将文件移动到目标目录 (File moving: move files to target directory)
- 文件清理：清理临时文件和目录 (File cleanup: clean temporary files and directories)
- 错误处理：处理文件操作中的各种异常 (Error handling: handle various file operation exceptions)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import shutil
import zipfile
from pathlib import Path
from typing import Optional

from .logging_utils import get_logger, download_logger
from .utils import print_info, print_success, print_warn, print_error

logger = get_logger(__name__)

def process_downloaded_file(zip_path: Path, music_dir: Path, temp_dir: Path) -> bool:
    """
    处理下载的文件
    ============
    
    将下载的ZIP文件解压并移动到音乐目录
    
    Args:
        zip_path: 下载的ZIP文件路径
        music_dir: 音乐目录路径
        temp_dir: 临时目录路径
    
    Returns:
        bool: 处理是否成功
    """
    try:
        if not zip_path.exists():
            print_error(f"下载文件不存在: {zip_path}")
            return False
        
        print_info(f"开始处理下载文件: {zip_path.name}")
        
        # 创建临时解压目录
        extract_dir = temp_dir / f"extract_{zip_path.stem}"
        extract_dir.mkdir(exist_ok=True)
        
        # 解压ZIP文件
        if not extract_zip_file(zip_path, extract_dir):
            return False
        
        # 移动音频文件到音乐目录
        if not move_audio_files(extract_dir, music_dir):
            return False
        
        # 清理临时文件
        cleanup_temp_files(zip_path, extract_dir)
        
        print_success(f"文件处理完成: {zip_path.name}")
        download_logger.log_file_operation("process_downloaded_file", zip_path, True)
        return True
        
    except Exception as e:
        print_error(f"处理下载文件失败: {e}")
        download_logger.log_file_operation("process_downloaded_file", zip_path, False)
        return False

def extract_zip_file(zip_path: Path, extract_dir: Path) -> bool:
    """
    解压ZIP文件
    ==========
    
    Args:
        zip_path: ZIP文件路径
        extract_dir: 解压目录路径
    
    Returns:
        bool: 解压是否成功
    """
    try:
        print_info(f"解压文件: {zip_path.name}")
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # 检查ZIP文件内容
            file_list = zip_ref.namelist()
            print_info(f"ZIP文件包含 {len(file_list)} 个文件")
            
            # 解压所有文件
            zip_ref.extractall(extract_dir)
            
        print_success(f"解压完成: {len(file_list)} 个文件")
        return True
        
    except zipfile.BadZipFile:
        print_error(f"无效的ZIP文件: {zip_path}")
        return False
    except Exception as e:
        print_error(f"解压失败: {e}")
        return False

def move_audio_files(extract_dir: Path, music_dir: Path) -> bool:
    """
    移动音频文件到音乐目录
    ==================
    
    Args:
        extract_dir: 解压目录路径
        music_dir: 音乐目录路径
    
    Returns:
        bool: 移动是否成功
    """
    try:
        # 支持的音频文件扩展名
        audio_extensions = {'.mp3', '.flac', '.wav', '.m4a', '.aac', '.ogg', '.opus'}
        
        moved_count = 0
        
        # 递归查找音频文件
        for file_path in extract_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in audio_extensions:
                # 生成目标文件路径
                target_path = music_dir / file_path.name
                
                # 如果目标文件已存在，添加序号
                counter = 1
                original_target = target_path
                while target_path.exists():
                    stem = original_target.stem
                    suffix = original_target.suffix
                    target_path = music_dir / f"{stem}_{counter}{suffix}"
                    counter += 1
                
                # 移动文件
                shutil.move(str(file_path), str(target_path))
                print_success(f"移动文件: {file_path.name} -> {target_path.name}")
                moved_count += 1
        
        if moved_count > 0:
            print_success(f"成功移动 {moved_count} 个音频文件")
            return True
        else:
            print_warn("未找到音频文件")
            return False
            
    except Exception as e:
        print_error(f"移动音频文件失败: {e}")
        return False

def cleanup_temp_files(zip_path: Path, extract_dir: Path):
    """
    清理临时文件
    ==========
    
    Args:
        zip_path: ZIP文件路径
        extract_dir: 解压目录路径
    """
    try:
        # 删除ZIP文件
        if zip_path.exists():
            zip_path.unlink()
            print_info(f"删除ZIP文件: {zip_path.name}")
        
        # 删除解压目录
        if extract_dir.exists():
            shutil.rmtree(extract_dir)
            print_info(f"删除解压目录: {extract_dir.name}")
            
    except Exception as e:
        print_warn(f"清理临时文件时出现警告: {e}")

def handle_temp_directory_cleanup(temp_dir: Path):
    """
    处理临时目录清理
    ==============
    
    清理临时目录中的所有文件和子目录
    
    Args:
        temp_dir: 临时目录路径
    """
    try:
        if not temp_dir.exists():
            return
        
        print_info(f"清理临时目录: {temp_dir}")
        
        # 删除目录中的所有内容
        for item in temp_dir.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)
        
        print_success("临时目录清理完成")
        
    except Exception as e:
        print_warn(f"清理临时目录时出现警告: {e}")

def ensure_directory_exists(directory: Path) -> bool:
    """
    确保目录存在
    ==========
    
    Args:
        directory: 目录路径
    
    Returns:
        bool: 目录是否存在或创建成功
    """
    try:
        directory.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print_error(f"创建目录失败 {directory}: {e}")
        return False

def get_file_size_mb(file_path: Path) -> float:
    """
    获取文件大小（MB）
    ===============
    
    Args:
        file_path: 文件路径
    
    Returns:
        float: 文件大小（MB）
    """
    try:
        if file_path.exists():
            return file_path.stat().st_size / (1024 * 1024)
        return 0.0
    except Exception:
        return 0.0

def is_audio_file(file_path: Path) -> bool:
    """
    检查是否为音频文件
    ================
    
    Args:
        file_path: 文件路径
    
    Returns:
        bool: 是否为音频文件
    """
    audio_extensions = {'.mp3', '.flac', '.wav', '.m4a', '.aac', '.ogg', '.opus', '.wma', '.aiff', '.alac'}
    return file_path.suffix.lower() in audio_extensions
