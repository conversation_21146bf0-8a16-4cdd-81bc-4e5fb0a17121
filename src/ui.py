#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户界面模块 - 增强的用户交互和显示系统
User Interface Module - Enhanced User Interaction and Display System

功能概述 (Feature Overview):
- 轻量级任务指示器：实时显示下载进度 (Lightweight task indicators: real-time download progress)
- 丰富的结果展示：详细的统计信息和成功率分析 (Rich result display: detailed statistics and success rate analysis)
- 彩色输出支持：使用颜色区分不同类型的信息 (Colored output support: use colors to distinguish different types of information)
- 进度条显示：可视化的进度指示 (Progress bar display: visual progress indication)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import re
from typing import Dict, List
from urllib.parse import urlparse, parse_qs

# 尝试导入colorama进行彩色输出
try:
    from colorama import Fore, Back, Style, init
    init(autoreset=True)
    COLORS_AVAILABLE = True
except ImportError:
    # 如果colorama不可用，定义空的颜色常量
    class _DummyColor:
        def __getattr__(self, name):
            return ""
    Fore = Back = Style = _DummyColor()
    COLORS_AVAILABLE = False

from .utils import print_error, print_info, print_success, print_warn

# ==================== 轻量级任务指示器 (Lightweight Task Indicators) ====================

def extract_song_id_from_url(url: str) -> str:
    """从URL中提取歌曲ID (Extract song ID from URL)"""
    try:
        # 解析URL
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)

        # 尝试从查询参数中获取ID
        if 'id' in query_params:
            return query_params['id'][0]

        # 尝试从路径中提取ID
        path_match = re.search(r'/(\d+)', parsed.path)
        if path_match:
            return path_match.group(1)

        # 如果都没找到，返回Unknown
        return "Unknown"
    except Exception:
        return "Unknown"

def create_mini_progress_bar(current: int, total: int, width: int = 10) -> str:
    """
    创建迷你进度条 (Create mini progress bar)
    =====================================

    基于Rust版本的进度指示器设计，提供更丰富的视觉效果。
    对应Rust版本的create_progress_bar函数功能。

    Args:
        current: 当前进度
        total: 总数
        width: 进度条宽度

    Returns:
        str: 格式化的进度条字符串
    """
    if total == 0:
        return "▱" * width

    progress = int((current / total) * width)
    filled = "▰" * progress
    empty = "▱" * (width - progress)
    return f"{filled}{empty}"

def create_lightweight_task_indicator(url: str, current: int, total: int) -> str:
    """
    创建增强的轻量级任务指示器 (Create enhanced lightweight task indicator)
    ================================================================

    基于Rust版本的create_progress_indicator设计理念，提供丰富的进度展示。
    对应Rust版本中的进度指示器功能，包含百分比、进度条和歌曲信息。

    功能特性 (Features):
    - 进度百分比显示 (Progress percentage display)
    - 可视化进度条 (Visual progress bar)
    - 歌曲信息提取 (Song information extraction)
    - 彩色输出支持 (Colored output support)

    Args:
        url: 歌曲URL
        current: 当前任务编号
        total: 总任务数

    Returns:
        str: 格式化的任务指示器字符串
    """
    song_id = extract_song_id_from_url(url)
    percentage = int((current / total) * 100) if total > 0 else 0

    # 创建进度条视觉效果 - 对应Rust版本的progress_bar
    progress_bar = create_mini_progress_bar(current, total, width=8)

    if COLORS_AVAILABLE:
        return (
            f"🎵 [{Fore.CYAN}{current}{Style.RESET_ALL}/"
            f"{Fore.CYAN}{total}{Style.RESET_ALL}] "
            f"{Fore.YELLOW}{percentage}%{Style.RESET_ALL} "
            f"{Fore.GREEN}{progress_bar}{Style.RESET_ALL} "
            f"歌曲ID: {Fore.BLUE}{song_id}{Style.RESET_ALL}"
        )
    else:
        return f"🎵 [{current}/{total}] {percentage}% {progress_bar} 歌曲ID: {song_id}"

# ==================== 用户输入界面 (User Input Interface) ====================

def get_user_url_input() -> str:
    """
    获取用户URL输入 - 增强版输入界面
    ================================

    提供友好的URL输入界面，包含输入验证、格式检查和帮助信息。
    支持多种网易云音乐URL格式，并提供实时反馈。

    支持的URL格式:
    - 单曲: https://music.163.com/song?id=123456
    - 歌单: https://music.163.com/playlist?id=123456
    - 专辑: https://music.163.com/album?id=123456
    - 移动端: https://music.163.com/#/song?id=123456

    Returns:
        str: 用户输入的有效URL

    Features:
        - 实时URL格式验证
        - 详细的帮助信息
        - 输入示例展示
        - 错误提示和建议
    """
    print("\n" + "=" * 70)
    print_success("🔗 URL输入 - 请输入网易云音乐链接")
    print("=" * 70)

    # 显示支持的URL格式
    print("📋 支持的链接格式:")
    print("   🎵 单曲: https://music.163.com/song?id=123456")
    print("   📀 专辑: https://music.163.com/album?id=123456")
    print("   📝 歌单: https://music.163.com/playlist?id=123456")
    print("   📱 移动端: https://music.163.com/#/song?id=123456")
    print()

    # 显示使用提示
    print("💡 使用提示:")
    print("   • 复制网易云音乐中的分享链接")
    print("   • 支持桌面端和移动端链接")
    print("   • 输入 'help' 查看详细帮助")
    print("   • 输入 'exit' 退出程序")
    print("=" * 70)

    while True:
        url_input = input("\n🔗 请输入网易云音乐URL: ").strip()

        # 处理特殊命令
        if url_input.lower() == 'exit':
            print_info("👋 感谢使用，再见！")
            exit(0)
        elif url_input.lower() == 'help':
            display_url_help()
            continue
        elif not url_input:
            print_warn("⚠️  URL不能为空，请输入有效的网易云音乐链接")
            continue

        # 基本URL格式验证
        if validate_url_format(url_input):
            print_success("✅ URL格式验证通过")
            return url_input
        else:
            print_error("❌ URL格式不正确")
            print_info("💡 请确保输入的是有效的网易云音乐链接")
            print_info("   输入 'help' 查看详细帮助和示例")


def validate_url_format(url: str) -> bool:
    """
    验证URL格式是否正确
    ==================

    检查输入的URL是否符合网易云音乐的链接格式。

    Args:
        url (str): 用户输入的URL

    Returns:
        bool: URL格式是否有效
    """
    import re

    # 网易云音乐URL的正则表达式模式
    patterns = [
        r'music\.163\.com/(?:#/)?song\?id=\d+',      # 单曲
        r'music\.163\.com/(?:#/)?playlist\?id=\d+',  # 歌单
        r'music\.163\.com/(?:#/)?album\?id=\d+',     # 专辑
    ]

    return any(re.search(pattern, url) for pattern in patterns)


def display_url_help() -> None:
    """
    显示URL输入帮助信息
    ==================

    提供详细的URL格式说明、获取方法和常见问题解答。
    """
    print("\n" + "=" * 70)
    print_success("📖 URL输入帮助")
    print("=" * 70)

    print("🎯 如何获取网易云音乐链接:")
    print("   1. 打开网易云音乐网页版或APP")
    print("   2. 找到想要下载的歌曲/专辑/歌单")
    print("   3. 点击分享按钮")
    print("   4. 复制链接")
    print("   5. 粘贴到这里")
    print()

    print("📝 链接格式示例:")
    print("   单曲: https://music.163.com/song?id=1234567890")
    print("   专辑: https://music.163.com/album?id=1234567890")
    print("   歌单: https://music.163.com/playlist?id=1234567890")
    print()

    print("❓ 常见问题:")
    print("   Q: 链接包含很多参数怎么办？")
    print("   A: 没关系，程序会自动提取有效部分")
    print()
    print("   Q: 移动端链接可以使用吗？")
    print("   A: 可以，支持所有网易云音乐链接格式")
    print()
    print("   Q: 链接无效怎么办？")
    print("   A: 请检查链接是否完整，或重新复制")
    print("=" * 70)


def display_program_header(music_dir_path: str, browser_data_dir_path: str) -> None:
    """
    显示程序头部信息和欢迎界面
    ============================

    展示程序的基本信息、版本号、功能特性和重要路径信息。
    为用户提供清晰的程序状态和配置信息。

    Args:
        music_dir_path (str): 音乐文件保存目录的完整路径
        browser_data_dir_path (str): 浏览器数据目录的完整路径

    Display Elements:
        - 程序标题和版本信息
        - 核心功能特性说明
        - 文件保存路径信息
        - 浏览器数据目录信息
        - 免责声明和使用提示
    """
    print("\n" + "=" * 80)
    print_success("🎵 nt-dl-py 网易云音乐下载器 v2.0")
    print_success("   企业级模块化架构 | 生产就绪版本")
    print("=" * 80)
    print("🚀 核心特性: 智能下载 | 自动重试 | 错误恢复 | 性能优化")
    print("🛡️ 安全特性: 数据隔离 | 自动清理 | 无痕模式 | 资源管理")
    print("📦 技术栈: Python 3.8+ | Playwright | FFmpeg | 模块化架构")
    print("-" * 80)
    print_info(f"📁 音乐保存目录: {music_dir_path}")
    print_info(f"🔒 浏览器数据目录: {browser_data_dir_path} (自动管理)")
    print("-" * 80)
    print("⚠️  重要提醒:")
    print("   • 本工具仅供技术学习和研究使用")
    print("   • 数据来源于公开互联网和第三方公益解析服务")
    print("   • 请遵守相关法律法规和版权规定")
    print("   • 支持正版音乐，购买合法授权")
    print("=" * 80)


def select_audio_quality() -> str:
    """
    音质选择交互界面
    ================

    为用户提供音质选择界面，包含详细的音质说明和推荐建议。
    支持智能默认选择和输入验证。

    音质选项:
    1. 超清母带 (SVIP): 最高音质，需要SVIP会员
    2. 高解析度无损 (VIP): 推荐选择，平衡音质和兼容性

    Returns:
        str: 用户选择的音质字符串
             - '超清母带(SVIP)': 最高音质选项
             - '高解析度无损(VIP)': 推荐音质选项

    User Experience:
        - 清晰的选项说明和对比
        - 智能默认推荐
        - 输入验证和错误提示
        - 选择确认和后续提醒
    """
    print("\n" + "=" * 70)
    print_success("🎵 音质选择 - 请选择下载音质")
    print("=" * 70)

    # 选项1: 超清母带
    print("1️⃣  超清母带 (SVIP)")
    print("   🎯 音质等级: 最高 (192kHz/24bit)")
    print("   📦 文件大小: 最大 (约50-100MB/首)")
    print("   🔑 会员要求: 需要SVIP会员")
    print("   ⚠️  兼容性: 部分设备可能不支持")
    print()

    # 选项2: 高解析度无损 (推荐)
    print("2️⃣  高解析度无损 (VIP) - 🌟 强烈推荐 🌟")
    print("   🎯 音质等级: 无损 (44.1kHz/16bit)")
    print("   📦 文件大小: 适中 (约15-30MB/首)")
    print("   🔑 会员要求: 无特殊要求")
    print("   ✅ 兼容性: 支持所有设备和播放器")
    print("   🚀 成功率: 解析成功率更高")
    print("   💡 推荐理由: 音质与体积的最佳平衡")
    print()

    print("=" * 70)
    print("💡 建议: 首次使用推荐选择选项2，获得最佳体验")
    print("=" * 70)

    while True:
        choice = input("\n请选择音质 [1/2] (直接回车选择推荐的选项2): ").strip()

        if choice == '' or choice == '2':
            print_success("\n✅ 已选择: 高解析度无损 (VIP) - 明智的选择！")
            print_info("🎵 将为您下载高质量无损音频文件")
            return '高解析度无损(VIP)'
        elif choice == '1':
            print_success("\n✅ 已选择: 超清母带 (SVIP)")
            print_warn("⚠️  注意: 如果解析失败将自动降级到高解析度无损")
            print_info("🎵 将尝试下载最高质量的母带音频")
            return '超清母带(SVIP)'
        else:
            print_error("❌ 输入无效！请输入 1 或 2，或直接回车选择推荐选项")
            print_info("💡 提示: 直接按回车键将选择推荐的高解析度无损音质")


def create_lightweight_task_indicator(url: str, current: int, total: int) -> str:
    """
    创建轻量级任务指示器
    
    Args:
        url: 歌曲URL
        current: 当前任务编号
        total: 总任务数
        
    Returns:
        任务指示器字符串
    """
    song_id = extract_song_id_from_url(url) or "Unknown"
    return f"🎵 [{current}/{total}] 歌曲ID: {song_id}"


def extract_song_id_from_url(url: str) -> str:
    """
    从URL中提取歌曲ID
    
    Args:
        url: 歌曲URL
        
    Returns:
        歌曲ID
    """
    match = re.search(r'song\?id=(\d+)', url)
    return match.group(1) if match else "Unknown"


# ==================== 增强结果显示 (Enhanced Result Display) ====================

def display_final_results(total_urls: List[str], failed_urls: Dict[str, str], music_dir_path: str) -> None:
    """
    显示最终结果 (Display final results)
    =================================

    基于Rust版本的display_final_results设计，提供完整的任务统计报告。
    对应Rust版本中的结果展示功能，包含统计信息、成功率分析和完成等级。

    功能特性 (Features):
    - 详细的统计信息 (Detailed statistics)
    - 成功率计算和等级评定 (Success rate calculation and grading)
    - 失败任务汇总 (Failed task summary)
    - 文件信息说明 (File information description)
    - 彩色视觉效果 (Colored visual effects)

    Args:
        total_urls: 总URL列表
        failed_urls: 失败URL字典
        music_dir_path: 音乐目录路径
    """
    total = len(total_urls)
    success = total - len(failed_urls)
    success_rate = (success / total * 100) if total > 0 else 0.0

    # 显示完成横幅 - 对应Rust版本的任务完成提示
    print("\n" + "=" * 70)
    if COLORS_AVAILABLE:
        print(f"{Fore.GREEN}🎉 全部处理完成 - 下载任务执行完毕{Style.RESET_ALL}")
    else:
        print("🎉 全部处理完成 - 下载任务执行完毕")
    print("=" * 70)

    # 显示任务统计报告 - 对应Rust版本的统计信息展示
    display_task_statistics(total, success, success_rate)

    # 显示成功信息 - 对应Rust版本的成功结果说明
    if success > 0:
        display_success_information()

    # 显示失败信息 - 对应Rust版本的失败任务汇总
    if failed_urls:
        display_failure_information(failed_urls)

    # 显示管理信息 - 对应Rust版本的文件管理说明
    display_management_information()

    # 显示文件位置信息
    print("=" * 70)
    print_info(f"📁 音乐文件保存在: {music_dir_path}")
    print("=" * 70)

def display_task_statistics(total: int, success: int, success_rate: float):
    """
    显示任务统计信息 (Display task statistics)
    ======================================

    基于Rust版本的统计信息展示设计，提供详细的任务执行报告。
    对应Rust版本中的display_final_results统计部分。
    """
    print_info("📊 任务统计报告:")

    if COLORS_AVAILABLE:
        print(f"   • 总任务数: {Fore.CYAN}{total} 首歌曲{Style.RESET_ALL}")
        print(f"   • 成功完成: {Fore.GREEN}{success} 首{Style.RESET_ALL}")
        print(f"   • 失败任务: {Fore.RED}{total - success} 首{Style.RESET_ALL}")
        print(f"   • 成功率: {Fore.YELLOW}{success_rate:.1f}%{Style.RESET_ALL}")
    else:
        print(f"   • 总任务数: {total} 首歌曲")
        print(f"   • 成功完成: {success} 首")
        print(f"   • 失败任务: {total - success} 首")
        print(f"   • 成功率: {success_rate:.1f}%")

    # 显示完成等级 - 对应Rust版本的成功率等级评定
    grade = get_completion_grade(success_rate)
    color = get_grade_color(success_rate)

    if COLORS_AVAILABLE:
        print(f"   • 完成等级: {color}{grade}{Style.RESET_ALL}")
    else:
        print(f"   • 完成等级: {grade}")

def get_completion_grade(success_rate: float) -> str:
    """
    获取完成等级 (Get completion grade)
    ===============================

    基于Rust版本的等级评定逻辑，根据成功率返回相应等级。
    """
    if success_rate >= 95.0:
        return "🏆 完美"
    elif success_rate >= 85.0:
        return "🥇 优秀"
    elif success_rate >= 70.0:
        return "🥈 良好"
    elif success_rate >= 50.0:
        return "🥉 需改进"
    else:
        return "❌ 待优化"

def get_grade_color(success_rate: float) -> str:
    """获取等级对应的颜色 (Get color for grade)"""
    if not COLORS_AVAILABLE:
        return ""

    if success_rate >= 85.0:
        return Fore.GREEN
    elif success_rate >= 70.0:
        return Fore.YELLOW
    else:
        return Fore.RED

def display_success_information():
    """
    显示成功信息 (Display success information)
    ======================================

    基于Rust版本的成功结果展示，说明下载文件的特性和质量。
    """
    print_success("✨ 下载的音频文件已包含:")
    print("   • 🎨 封面图片 (嵌入到音频文件中)")
    print("   • 📝 歌词信息 (嵌入到metadata中)")
    print("   • 🏷️  完整的标签信息 (艺术家、专辑、标题等)")
    print("   • 🎵 高质量音频 (根据可用性自动选择最佳音质)")
    print("   • 📁 智能文件命名 (便于管理和识别)")

def display_management_information():
    """
    显示管理信息 (Display management information)
    ==========================================

    基于Rust版本的文件管理说明，对应安全特性和资源管理部分。
    """
    print_info("🗂️  集中管理：所有程序文件都在下载目录下")
    print_info("🔒 隐藏browser_data目录，避免用户混淆")
    print_info("🧹 自动清理：启动和退出时智能清理遗留文件")

def display_failure_information(failed_urls: Dict[str, str]):
    """显示失败信息 (Display failure information)"""
    print_warn("❌ 失败任务详情:")

    # 显示前5个失败项目
    for i, (url, error) in enumerate(list(failed_urls.items())[:5], 1):
        song_id = extract_song_id_from_url(url)
        print(f"   {i}. 歌曲ID: {song_id} - {error}")

    if len(failed_urls) > 5:
        print(f"   ... 还有 {len(failed_urls) - 5} 个失败项目")

    # 提供解决建议 - 对应Rust版本的错误诊断和解决方案
    print_info("💡 失败原因可能包括:")
    print("   • 网络连接不稳定")
    print("   • 歌曲已下架或地区限制")
    print("   • 第三方解析服务暂时不可用")
    print("   • 系统资源不足")


def display_legal_notice() -> None:
    """
    显示法律声明和使用提醒
    ======================

    在程序启动时显示重要的法律声明和使用条款提醒，
    确保用户了解软件的合法使用方式和责任边界。
    """
    print("\n" + "⚖️" + " " * 30 + "法律声明" + " " * 30 + "⚖️")
    print("=" * 80)
    print_warn("📋 使用前必读:")
    print("   • 本软件仅供技术学习和研究使用")
    print("   • 数据来源于公开互联网和第三方公益解析服务")
    print("   • 请遵守当地法律法规和版权规定")
    print("   • 支持正版音乐，购买合法授权")
    print("   • 详细条款请查看 docs/LEGAL_DISCLAIMER.md")
    print()
    print_info("🎯 合法使用建议:")
    print("   • 仅下载已购买或有合法使用权的音乐")
    print("   • 不得用于商业用途或大规模分发")
    print("   • 控制使用频率，避免对服务器造成压力")
    print("   • 如有疑问，请咨询相关法律专业人士")
    print("=" * 80)

    # 用户确认
    while True:
        confirm = input("\n是否同意以上条款并继续使用？[Y/n]: ").strip().lower()
        if confirm in ['', 'y', 'yes', '是', '同意']:
            print_success("✅ 已确认同意使用条款，程序继续运行")
            break
        elif confirm in ['n', 'no', '否', '不同意']:
            print_info("👋 感谢您的理解，程序退出")
            exit(0)
        else:
            print_warn("请输入 Y(同意) 或 N(不同意)")
    print()