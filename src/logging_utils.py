#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块 - Logging Utilities Module
=====================================

提供结构化日志系统，支持多级日志、彩色输出、文件记录
Provides structured logging system with multi-level logging, colored output, file recording

功能概述 (Feature Overview):
- 多级日志：支持DEBUG、INFO、WARNING、ERROR、CRITICAL级别 (Multi-level logging)
- 彩色输出：控制台输出支持颜色区分 (Colored console output)
- 文件记录：支持日志文件持久化存储 (File logging)
- 结构化：支持结构化字段记录 (Structured logging)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# 彩色输出支持
try:
    import colorama
    from colorama import Fore, Back, Style
    colorama.init(autoreset=True)
    COLORS_AVAILABLE = True
except ImportError:
    COLORS_AVAILABLE = False
    # 定义空的颜色常量
    class _DummyColor:
        def __getattr__(self, name):
            return ""
    Fore = Back = Style = _DummyColor()

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器 (Colored log formatter)"""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT,
    }
    
    def format(self, record):
        if COLORS_AVAILABLE:
            # 添加颜色
            color = self.COLORS.get(record.levelname, '')
            record.levelname = f"{color}{record.levelname}{Style.RESET_ALL}"
            record.name = f"{Fore.BLUE}{record.name}{Style.RESET_ALL}"
        
        return super().format(record)

class LogConfig:
    """日志配置 (Log Configuration)"""
    
    def __init__(
        self,
        level: str = "INFO",
        enable_file_logging: bool = True,
        log_dir: str = "logs",
        enable_colors: bool = True,
        show_timestamps: bool = True,
        show_modules: bool = False,
    ):
        self.level = level.upper()
        self.enable_file_logging = enable_file_logging
        self.log_dir = Path(log_dir)
        self.enable_colors = enable_colors and COLORS_AVAILABLE
        self.show_timestamps = show_timestamps
        self.show_modules = show_modules

def setup_logging(config: Optional[LogConfig] = None) -> None:
    """设置日志系统 (Setup logging system)"""
    if config is None:
        config = LogConfig()
    
    # 创建日志目录
    if config.enable_file_logging:
        config.log_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 创建格式化器
    if config.show_timestamps:
        if config.show_modules:
            console_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            file_format = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        else:
            console_format = "%(asctime)s - %(levelname)s - %(message)s"
            file_format = "%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    else:
        if config.show_modules:
            console_format = "%(name)s - %(levelname)s - %(message)s"
            file_format = "%(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        else:
            console_format = "%(levelname)s - %(message)s"
            file_format = "%(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, config.level))
    
    if config.enable_colors:
        console_formatter = ColoredFormatter(console_format)
    else:
        console_formatter = logging.Formatter(console_format)
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if config.enable_file_logging:
        log_file = config.log_dir / f"nt_dl_py_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 使用RotatingFileHandler避免日志文件过大
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别
        
        file_formatter = logging.Formatter(file_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # 记录日志系统初始化
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成")
    logger.info(f"日志级别: {config.level}")
    logger.info(f"文件日志: {'启用' if config.enable_file_logging else '禁用'}")
    if config.enable_file_logging:
        logger.info(f"日志目录: {config.log_dir}")

def get_logger(name: str) -> logging.Logger:
    """获取日志器 (Get logger)"""
    return logging.getLogger(name)

class DownloadLogger:
    """专门的下载事件记录器 (Specialized download event logger)"""
    
    def __init__(self):
        self.logger = get_logger("download")
    
    def log_download_start(self, url: str, quality: str):
        """记录下载开始 (Log download start)"""
        self.logger.info(f"开始下载 - URL: {url}, 音质: {quality}")
    
    def log_download_success(
        self,
        url: str,
        file_size: int,
        duration_secs: float,
        speed_mbps: float,
        quality: str,
    ):
        """记录下载成功 (Log download success)"""
        self.logger.info(
            f"下载成功 - URL: {url}, "
            f"大小: {file_size / 1024 / 1024:.1f}MB, "
            f"耗时: {duration_secs:.1f}s, "
            f"速度: {speed_mbps:.1f}MB/s, "
            f"音质: {quality}"
        )
    
    def log_download_failure(self, url: str, error: str, quality: str):
        """记录下载失败 (Log download failure)"""
        self.logger.error(f"下载失败 - URL: {url}, 错误: {error}, 音质: {quality}")
    
    def log_environment_setup(self, music_dir: Path, temp_dir: Path, browser_dir: Path):
        """记录环境设置 (Log environment setup)"""
        self.logger.info(f"环境设置完成:")
        self.logger.info(f"  音乐目录: {music_dir}")
        self.logger.info(f"  临时目录: {temp_dir}")
        self.logger.info(f"  浏览器数据: {browser_dir}")
    
    def log_browser_action(self, action: str, details: str):
        """记录浏览器操作 (Log browser operations)"""
        self.logger.debug(f"浏览器操作 - 动作: {action}, 详情: {details}")
    
    def log_api_call(self, endpoint: str, status: int, duration_ms: int):
        """记录API调用 (Log API calls)"""
        self.logger.info(f"API调用 - 端点: {endpoint}, 状态: {status}, 耗时: {duration_ms}ms")
    
    def log_file_operation(self, operation: str, path: Path, success: bool):
        """记录文件操作 (Log file operations)"""
        if success:
            self.logger.info(f"文件操作成功 - 操作: {operation}, 路径: {path}")
        else:
            self.logger.warning(f"文件操作失败 - 操作: {operation}, 路径: {path}")
    
    def log_config_loaded(self, config_path: Path, valid: bool):
        """记录配置加载 (Log configuration loading)"""
        if valid:
            self.logger.info(f"配置文件加载成功: {config_path}")
        else:
            self.logger.warning(f"配置文件加载失败或无效: {config_path}")

# 全局下载日志器实例
download_logger = DownloadLogger()

def get_default_log_config() -> LogConfig:
    """获取默认日志配置 (Get default log configuration)"""
    config = LogConfig()
    
    # 根据环境变量调整配置
    if os.getenv("DEBUG"):
        config.level = "DEBUG"
        config.show_modules = True
    
    # 检查是否在开发模式
    if __debug__:
        config.level = "DEBUG"
        config.show_modules = True
    
    return config

# 自动初始化日志系统
if not logging.getLogger().handlers:
    setup_logging(get_default_log_config())
