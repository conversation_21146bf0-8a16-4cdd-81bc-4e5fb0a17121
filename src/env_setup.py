#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境管理模块 - Environment Management Module
==========================================

提供统一的环境状态管理和清理机制
Provides unified environment state management and cleanup mechanisms

功能概述 (Feature Overview):
- 环境初始化：创建必要的目录和配置 (Environment initialization)
- 状态管理：统一管理环境状态 (State management)
- 资源清理：确保资源正确释放 (Resource cleanup)
- 生命周期：完整的环境生命周期管理 (Lifecycle management)

⚠️ 重要声明:
本模块仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
"""

import atexit
import os
import shutil
import tempfile
import uuid
from pathlib import Path
from typing import Optional, Dict, Any

from .logging_utils import get_logger
from .utils import print_info, print_warn, print_error

logger = get_logger(__name__)

class EnvironmentManager:
    """环境管理器 (Environment Manager)"""
    
    def __init__(self):
        self.music_dir: Optional[Path] = None
        self.temp_dir: Optional[Path] = None
        self.current_browser_data_dir: Optional[Path] = None
        self.is_initialized = False
        self._cleanup_registered = False
    
    def initialize_environment(self, config: Dict[str, Any]) -> bool:
        """初始化环境 (Initialize environment)"""
        try:
            logger.info("开始环境初始化")
            
            # 设置音乐目录
            self._setup_music_directory(config)
            
            # 设置临时目录
            self._setup_temp_directory()
            
            # 设置浏览器数据目录
            self._setup_browser_data_directory()
            
            # 注册清理函数
            self._register_cleanup()
            
            # 记录环境设置完成
            logger.info("环境设置记录完成")
            
            self.is_initialized = True
            logger.info("环境初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"环境初始化失败: {e}")
            return False
    
    def _setup_music_directory(self, config: Dict[str, Any]):
        """设置音乐目录 (Setup music directory)"""
        import sys

        # 检查命令行参数
        if len(sys.argv) > 1:
            specified_path = Path(sys.argv[1])
            self.music_dir = specified_path
            self.music_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"✓ 使用命令行指定的下载目录: {self.music_dir}")
            return

        # 检查配置文件中的自定义目录
        custom_dir = config.get('custom_download_directory', '')
        if custom_dir:
            custom_path = Path(custom_dir)
            if custom_path.is_absolute() or str(custom_path).startswith('.'):
                self.music_dir = custom_path
                self.music_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"✓ 使用配置文件指定的下载目录: {self.music_dir}")
                return

        # 使用跨平台默认下载目录
        self.music_dir = self._get_cross_platform_downloads_dir()
        self.music_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ 使用默认下载目录: {self.music_dir}")
        logger.info("💡 提示: 下次可通过命令行参数或配置文件指定自定义路径")

    def _get_cross_platform_downloads_dir(self) -> Path:
        """获取跨平台下载目录 (Get cross-platform downloads directory)"""
        # 尝试获取用户的Downloads目录
        try:
            import os
            if os.name == 'nt':  # Windows
                downloads_dir = Path.home() / "Downloads"
            else:  # macOS and Linux
                downloads_dir = Path.home() / "Downloads"

            if downloads_dir.exists():
                return downloads_dir / "nt_dl_downloads"
        except Exception:
            pass

        # 回退到home目录 + Downloads
        try:
            home_dir = Path.home()
            return home_dir / "Downloads" / "nt_dl_downloads"
        except Exception:
            pass

        # 最终回退到当前目录
        return Path.cwd() / "nt_dl_downloads"
    
    def _setup_temp_directory(self):
        """设置临时目录 (Setup temporary directory)"""
        # 在下载目录下创建临时目录（与Rust版本一致）
        self.temp_dir = self.music_dir / ".temp_downloads"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"临时目录设置: {self.temp_dir}")
    
    def _setup_browser_data_directory(self):
        """设置浏览器数据目录 (Setup browser data directory)"""
        # 在下载目录下创建唯一的浏览器数据目录（与Rust版本一致）
        import time
        process_id = os.getpid()
        timestamp = int(time.time())

        # 格式：.browser_data_进程ID_时间戳（隐藏目录）
        dir_name = f".browser_data_{process_id}_{timestamp}"
        self.current_browser_data_dir = self.music_dir / dir_name
        self.current_browser_data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"浏览器数据目录设置: {self.current_browser_data_dir}")
    
    def _register_cleanup(self):
        """注册清理函数 (Register cleanup function)"""
        if not self._cleanup_registered:
            atexit.register(self.cleanup_environment)
            self._cleanup_registered = True
            logger.info("清理函数已注册")
    
    def cleanup_environment(self):
        """清理环境 (Cleanup environment)"""
        if not self.is_initialized:
            return
        
        logger.info("开始环境清理")
        
        try:
            # 清理浏览器数据目录
            if self.current_browser_data_dir and self.current_browser_data_dir.exists():
                shutil.rmtree(self.current_browser_data_dir, ignore_errors=True)
                logger.info(f"已清理浏览器数据目录: {self.current_browser_data_dir}")
            
            # 清理临时目录中的文件（保留目录）
            if self.temp_dir and self.temp_dir.exists():
                for item in self.temp_dir.iterdir():
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item, ignore_errors=True)
                logger.info(f"已清理临时目录: {self.temp_dir}")
            
            # 清理其他可能的遗留文件
            self._cleanup_legacy_files()
            
            logger.info("环境清理完成")
            
        except Exception as e:
            logger.error(f"环境清理失败: {e}")
    
    def _cleanup_legacy_files(self):
        """清理下载目录中遗留的browser_data目录 (Cleanup legacy browser_data directories in downloads)"""
        if not self.music_dir:
            return

        logger.info("🧹 检查下载目录中遗留的browser_data目录...")

        cleaned_count = 0
        total_size_freed = 0

        try:
            # 扫描下载目录中的browser_data相关目录
            for item in self.music_dir.iterdir():
                if item.is_dir() and item.name.startswith('.browser_data_'):
                    try:
                        # 计算目录大小
                        dir_size = self._get_directory_size(item)

                        # 删除目录
                        shutil.rmtree(item, ignore_errors=True)

                        cleaned_count += 1
                        total_size_freed += dir_size
                        logger.info(f"清理遗留浏览器数据目录: {item}")
                    except Exception as e:
                        logger.warning(f"清理遗留目录失败 {item}: {e}")

            if cleaned_count > 0:
                size_mb = total_size_freed / (1024 * 1024)
                logger.info(f"✓ 清理完成: 删除了 {cleaned_count} 个遗留目录，释放空间 {size_mb:.1f} MB")
            else:
                logger.info("✓ 无需清理: 未发现遗留的browser_data目录")

        except Exception as e:
            logger.warning(f"清理遗留文件时出现警告: {e}")

    def _get_directory_size(self, directory: Path) -> int:
        """计算目录大小 (Calculate directory size)"""
        total_size = 0
        try:
            for item in directory.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
        except Exception:
            pass
        return total_size
    
    def get_music_directory(self) -> Path:
        """获取音乐目录 (Get music directory)"""
        if not self.music_dir:
            raise RuntimeError("环境未初始化，请先调用 initialize_environment()")
        return self.music_dir
    
    def get_temp_directory(self) -> Path:
        """获取临时目录 (Get temporary directory)"""
        if not self.temp_dir:
            raise RuntimeError("环境未初始化，请先调用 initialize_environment()")
        return self.temp_dir
    
    def get_current_browser_data_dir(self) -> Path:
        """获取当前浏览器数据目录 (Get current browser data directory)"""
        if not self.current_browser_data_dir:
            raise RuntimeError("环境未初始化，请先调用 initialize_environment()")
        return self.current_browser_data_dir
    
    def is_environment_ready(self) -> bool:
        """检查环境是否就绪 (Check if environment is ready)"""
        return (
            self.is_initialized and
            self.music_dir and self.music_dir.exists() and
            self.temp_dir and self.temp_dir.exists() and
            self.current_browser_data_dir and self.current_browser_data_dir.exists()
        )

# 全局环境管理器实例 (Global environment manager instance)
env_manager = EnvironmentManager()

def initialize_environment(config: Dict[str, Any]) -> bool:
    """初始化环境的便捷函数 (Convenience function for environment initialization)"""
    return env_manager.initialize_environment(config)

def cleanup_environment():
    """清理环境的便捷函数 (Convenience function for environment cleanup)"""
    env_manager.cleanup_environment()

def get_music_directory() -> Path:
    """获取音乐目录的便捷函数 (Convenience function to get music directory)"""
    return env_manager.get_music_directory()

def get_temp_directory() -> Path:
    """获取临时目录的便捷函数 (Convenience function to get temp directory)"""
    return env_manager.get_temp_directory()

def get_current_browser_data_dir() -> Path:
    """获取浏览器数据目录的便捷函数 (Convenience function to get browser data directory)"""
    return env_manager.get_current_browser_data_dir()

def is_environment_ready() -> bool:
    """检查环境是否就绪的便捷函数 (Convenience function to check if environment is ready)"""
    return env_manager.is_environment_ready()
