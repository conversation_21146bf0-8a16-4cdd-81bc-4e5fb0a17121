# 网易云音乐下载器 - Git忽略文件配置
# ===============================================

# ==================== Python相关 ====================
# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包相关
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller相关
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# ==================== IDE和编辑器 ====================
# PyCharm
.idea/

# VSCode
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==================== 系统文件 ====================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# ==================== 项目特定 ====================
# 下载的音乐文件
downloads/
music/
*.mp3
*.flac
*.m4a
*.wav

# 临时文件
temp/
tmp/
*.tmp
tmp_*

# 日志文件
*.log
logs/
.logs/

# 配置文件（可能包含敏感信息）
config.local.py
.env.local
.config_salt

# 浏览器数据目录
.browser_data_*
browser_data_*

# 缓存目录
.cache/
cache/
.nt_dl_cache/

# 测试输出
test_output/
test_downloads/

# 性能分析文件
*.prof

# ==================== 文档生成 ====================
# Sphinx文档
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# ==================== 其他 ====================
# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 数据库文件
*.db
*.sqlite
*.sqlite3