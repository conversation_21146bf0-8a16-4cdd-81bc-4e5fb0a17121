#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台环境自动化配置脚本
========================

本脚本用于自动化配置 nt-dl-py 的运行环境，包括：
- Python虚拟环境的创建和激活
- 依赖包的安装和验证
- Playwright浏览器内核的下载和配置
- 跨平台兼容性检查和优化
- 环境变量的设置和配置

支持的平台：
- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+
- 其他主流Linux发行版

使用方法：
python setup_environment.py [--force] [--no-browser] [--verbose]
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
from typing import List, Tuple, Optional
import argparse


class Colors:
    """终端颜色定义"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'


def print_info(message: str) -> None:
    """打印信息消息"""
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.END}")


def print_success(message: str) -> None:
    """打印成功消息"""
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")


def print_warning(message: str) -> None:
    """打印警告消息"""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")


def print_error(message: str) -> None:
    """打印错误消息"""
    print(f"{Colors.RED}❌ {message}{Colors.END}")


def print_header(message: str) -> None:
    """打印标题消息"""
    print(f"\n{Colors.BOLD}{Colors.CYAN}{'='*60}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.CYAN}{message:^60}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.CYAN}{'='*60}{Colors.END}\n")


class EnvironmentSetup:
    """环境配置管理器"""
    
    def __init__(self, force: bool = False, no_browser: bool = False, verbose: bool = False):
        self.force = force
        self.no_browser = no_browser
        self.verbose = verbose
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.system_info = self._get_system_info()
        
    def _get_system_info(self) -> dict:
        """获取系统信息"""
        return {
            'platform': platform.system(),
            'platform_release': platform.release(),
            'platform_version': platform.version(),
            'architecture': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'python_implementation': platform.python_implementation(),
        }
    
    def _run_command(self, command: List[str], check: bool = True, capture_output: bool = True) -> subprocess.CompletedProcess:
        """执行系统命令"""
        if self.verbose:
            print_info(f"执行命令: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command,
                check=check,
                capture_output=capture_output,
                text=True,
                cwd=self.project_root
            )
            if self.verbose and result.stdout:
                print(f"输出: {result.stdout}")
            return result
        except subprocess.CalledProcessError as e:
            print_error(f"命令执行失败: {' '.join(command)}")
            if e.stdout:
                print(f"标准输出: {e.stdout}")
            if e.stderr:
                print(f"错误输出: {e.stderr}")
            raise
    
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        print_header("系统要求检查")
        
        # 检查Python版本
        python_version = tuple(map(int, platform.python_version().split('.')))
        if python_version < (3, 8):
            print_error(f"Python版本过低: {platform.python_version()}, 需要3.8+")
            return False
        print_success(f"Python版本: {platform.python_version()} ✓")
        
        # 检查pip
        try:
            result = self._run_command([sys.executable, "-m", "pip", "--version"])
            print_success(f"pip可用: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            print_error("pip不可用，请安装pip")
            return False
        
        # 检查磁盘空间
        free_space = shutil.disk_usage(self.project_root).free / (1024**3)  # GB
        if free_space < 2:
            print_warning(f"磁盘空间不足: {free_space:.1f}GB，建议至少2GB")
        else:
            print_success(f"磁盘空间充足: {free_space:.1f}GB")
        
        # 显示系统信息
        print_info("系统信息:")
        for key, value in self.system_info.items():
            print(f"  {key}: {value}")
        
        return True
    
    def setup_virtual_environment(self) -> bool:
        """设置虚拟环境"""
        print_header("虚拟环境配置")
        
        if self.venv_path.exists() and not self.force:
            print_info("虚拟环境已存在，跳过创建")
            return True
        
        if self.venv_path.exists() and self.force:
            print_warning("强制模式：删除现有虚拟环境")
            shutil.rmtree(self.venv_path)
        
        try:
            print_info("创建Python虚拟环境...")
            self._run_command([sys.executable, "-m", "venv", str(self.venv_path)])
            print_success("虚拟环境创建成功")
            
            # 获取虚拟环境中的Python路径
            if self.system_info['platform'] == 'Windows':
                venv_python = self.venv_path / "Scripts" / "python.exe"
                venv_pip = self.venv_path / "Scripts" / "pip.exe"
            else:
                venv_python = self.venv_path / "bin" / "python"
                venv_pip = self.venv_path / "bin" / "pip"
            
            # 升级pip
            print_info("升级pip到最新版本...")
            self._run_command([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"])
            print_success("pip升级完成")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print_error(f"虚拟环境创建失败: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖包"""
        print_header("依赖包安装")
        
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            print_error("requirements.txt文件不存在")
            return False
        
        # 获取虚拟环境中的Python路径
        if self.system_info['platform'] == 'Windows':
            venv_python = self.venv_path / "Scripts" / "python.exe"
        else:
            venv_python = self.venv_path / "bin" / "python"
        
        try:
            print_info("安装项目依赖...")
            self._run_command([
                str(venv_python), "-m", "pip", "install", 
                "-r", str(requirements_file)
            ])
            print_success("依赖包安装完成")
            
            # 验证关键依赖
            print_info("验证关键依赖包...")
            key_packages = ["playwright", "requests", "beautifulsoup4", "mutagen"]
            for package in key_packages:
                try:
                    self._run_command([
                        str(venv_python), "-c", f"import {package}; print('{package} imported successfully')"
                    ])
                    print_success(f"{package} ✓")
                except subprocess.CalledProcessError:
                    print_error(f"{package} 导入失败")
                    return False
            
            return True
            
        except subprocess.CalledProcessError as e:
            print_error(f"依赖包安装失败: {e}")
            return False
    
    def setup_playwright_browsers(self) -> bool:
        """设置Playwright浏览器"""
        if self.no_browser:
            print_info("跳过浏览器安装（--no-browser选项）")
            return True
            
        print_header("Playwright浏览器配置")
        
        # 获取虚拟环境中的Python路径
        if self.system_info['platform'] == 'Windows':
            venv_python = self.venv_path / "Scripts" / "python.exe"
        else:
            venv_python = self.venv_path / "bin" / "python"
        
        try:
            print_info("安装Playwright浏览器内核...")
            self._run_command([
                str(venv_python), "-m", "playwright", "install", "chromium"
            ])
            print_success("Chromium浏览器安装完成")
            
            # 安装系统依赖（Linux）
            if self.system_info['platform'] == 'Linux':
                print_info("安装系统依赖...")
                try:
                    self._run_command([
                        str(venv_python), "-m", "playwright", "install-deps"
                    ])
                    print_success("系统依赖安装完成")
                except subprocess.CalledProcessError:
                    print_warning("系统依赖安装失败，可能需要手动安装")
            
            # 验证浏览器安装
            print_info("验证浏览器安装...")
            self._run_command([
                str(venv_python), "-c", 
                "from playwright.sync_api import sync_playwright; "
                "p = sync_playwright().start(); "
                "browser = p.chromium.launch(); "
                "browser.close(); "
                "p.stop(); "
                "print('Browser test successful')"
            ])
            print_success("浏览器验证成功")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print_error(f"浏览器配置失败: {e}")
            return False
    
    def create_activation_scripts(self) -> bool:
        """创建环境激活脚本"""
        print_header("创建激活脚本")
        
        try:
            # Windows批处理脚本
            if self.system_info['platform'] == 'Windows':
                activate_script = self.project_root / "activate.bat"
                with open(activate_script, 'w', encoding='utf-8') as f:
                    f.write(f"""@echo off
echo 激活 nt-dl-py 虚拟环境...
call "{self.venv_path}\\Scripts\\activate.bat"
echo 环境已激活！可以运行: python main.py
cmd /k
""")
                print_success("Windows激活脚本创建完成: activate.bat")
            
            # Unix shell脚本
            activate_script = self.project_root / "activate.sh"
            with open(activate_script, 'w', encoding='utf-8') as f:
                f.write(f"""#!/bin/bash
echo "激活 nt-dl-py 虚拟环境..."
source "{self.venv_path}/bin/activate"
echo "环境已激活！可以运行: python main.py"
exec "$SHELL"
""")
            
            # 设置执行权限
            if self.system_info['platform'] != 'Windows':
                os.chmod(activate_script, 0o755)
            
            print_success("Unix激活脚本创建完成: activate.sh")
            
            return True
            
        except Exception as e:
            print_error(f"激活脚本创建失败: {e}")
            return False
    
    def run_final_test(self) -> bool:
        """运行最终测试"""
        print_header("最终测试")
        
        # 获取虚拟环境中的Python路径
        if self.system_info['platform'] == 'Windows':
            venv_python = self.venv_path / "Scripts" / "python.exe"
        else:
            venv_python = self.venv_path / "bin" / "python"
        
        try:
            print_info("测试主程序导入...")
            self._run_command([
                str(venv_python), "-c", 
                "import main; print('Main module imported successfully')"
            ])
            print_success("主程序导入测试通过")
            
            print_info("测试所有模块导入...")
            test_script = """
import sys
sys.path.append('.')
modules = ['src.config', 'src.crypto', 'src.api', 'src.utils', 'src.ui', 'src.downloader']
for module in modules:
    __import__(module)
    print(f'{module} ✓')
print('All modules imported successfully')
"""
            self._run_command([str(venv_python), "-c", test_script])
            print_success("所有模块导入测试通过")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print_error(f"最终测试失败: {e}")
            return False
    
    def setup(self) -> bool:
        """执行完整的环境设置"""
        print_header("nt-dl-py 环境自动化配置")
        print_info(f"项目路径: {self.project_root}")
        print_info(f"系统平台: {self.system_info['platform']} {self.system_info['platform_release']}")
        
        steps = [
            ("系统要求检查", self.check_system_requirements),
            ("虚拟环境配置", self.setup_virtual_environment),
            ("依赖包安装", self.install_dependencies),
            ("浏览器配置", self.setup_playwright_browsers),
            ("激活脚本创建", self.create_activation_scripts),
            ("最终测试", self.run_final_test),
        ]
        
        for step_name, step_func in steps:
            try:
                if not step_func():
                    print_error(f"步骤失败: {step_name}")
                    return False
            except KeyboardInterrupt:
                print_warning("\n用户中断了安装过程")
                return False
            except Exception as e:
                print_error(f"步骤异常: {step_name} - {e}")
                return False
        
        print_header("环境配置完成")
        print_success("🎉 nt-dl-py 环境配置成功！")
        print_info("使用方法:")
        if self.system_info['platform'] == 'Windows':
            print("  Windows: 双击 activate.bat 或运行 activate.bat")
        print("  Unix/Linux/macOS: 运行 ./activate.sh 或 bash activate.sh")
        print("  然后执行: python main.py")
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="nt-dl-py 环境自动化配置脚本")
    parser.add_argument("--force", action="store_true", help="强制重新创建虚拟环境")
    parser.add_argument("--no-browser", action="store_true", help="跳过浏览器安装")
    parser.add_argument("--verbose", action="store_true", help="显示详细输出")
    
    args = parser.parse_args()
    
    setup = EnvironmentSetup(
        force=args.force,
        no_browser=args.no_browser,
        verbose=args.verbose
    )
    
    success = setup.setup()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
